{"name": "星愿赏", "appid": "wx027b57b9479584d0", "description": "", "versionName": "1.0.0", "versionCode": "100", "transformPx": false, "app-plus": {"usingComponents": true, "nvueStyleCompiler": "uni-app", "compilerVersion": 3, "splashscreen": {"alwaysShowBeforeRender": true, "waiting": true, "autoclose": true, "delay": 0}, "modules": {}, "distribute": {"android": {"permissions": ["<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>", "<uses-permission android:name=\"android.permission.VIBRATE\"/>", "<uses-permission android:name=\"android.permission.READ_LOGS\"/>", "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>", "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>", "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.CAMERA\"/>", "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>", "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>", "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>", "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>", "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>", "<uses-feature android:name=\"android.hardware.camera\"/>", "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"], "minSdkVersion": 30, "targetSdkVersion": 30, "abiFilters": ["armeabi-v7a", "arm64-v8a"]}, "ios": {}, "sdkConfigs": {"oauth": {"weixin": {"appid": "wx027b57b9479584d0", "UniversalLinks": ""}}, "payment": {"weixin": {"__platform__": ["ios", "android"], "appid": "wx027b57b9479584d0", "UniversalLinks": ""}}}}, "compatible": {"ignoreVersion": true}}, "quickapp": {}, "mp-weixin": {"appid": "wxb56964dd28204de0", "setting": {"urlCheck": false}, "usingComponents": true}, "mp-alipay": {"usingComponents": true, "styleIsolation": "shared"}, "mp-baidu": {"usingComponents": true}, "mp-toutiao": {"usingComponents": true}, "uniStatistics": {"enable": false}, "vueVersion": "3", "locale": "zh-Hans", "h5": {"router": {"base": "./"}}}