import axios from 'axios'
import { createUniAppAxiosAdapter } from '@uni-helper/axios-adapter'
import { httpLogError, requestError, throttleToLogin, httpRequestError } from './utils'
import { userAPI } from '@/api/modules/user'

const PREFIX = import.meta.env.VITE_APP_PREFIX

const requestQueue = [] // 存储等待重新请求的请求
let isRefreshing = false // 是否正在刷新 token

export function createService() {
  const request = axios.create({ adapter: createUniAppAxiosAdapter() })

  request.interceptors.request.use(
    (config) => {
      const token = uni.getStorageSync(`token`)
      if (token) {
        config.headers = {
          Authorization: `${token}`, // 或者你服务端定义的格式
          ...config.headers
        }
      }
      return config
    },
    (err) => {
      return Promise.reject(err)
    }
  )
  request.interceptors.response.use(
    async (response) => {
      const dataAxios = response.data
      // 这个状态码是和后端约定的
      const { code, data } = dataAxios
      // 根据 code 进行判断
      if (code === undefined) {
        return dataAxios
      } else if (`${code}` === '401') {
        const token = uni.getStorageSync('token')
        if (!token) {
          // 没有 unionid，跳登录
          // throttleToLogin()
          return throttleToLogin()
        }
        const userStore = useOutsideUserStore()
        const config = response.config
        // 创建一个 Promise，把请求加入请求队列
        const retryRequest = new Promise((resolve) => {
          requestQueue.push((newToken) => {
            // 重新设置 token，再重新发起请求
            config.headers.Authorization = `${newToken}`
            resolve(request(config)) // 替换为你封装的 request 方法
          })
        })

        if (!isRefreshing) {
          isRefreshing = true
          try {
            uni.removeStorageSync('token') // 清除旧的 token
            const tokenRes = await userAPI.refreshToken({ unionid, loginType: 2 })
            const newToken = tokenRes.token
            uni.setStorageSync('token', newToken)
            setTimeout(() => {
              userStore.getUserInfo()
            }, 1000)
            isRefreshing = false

            // 依次执行缓存的请求
            requestQueue.forEach((cb) => cb(newToken))
            requestQueue.length = 0 // 清空队列
          } catch (e) {
            isRefreshing = false
            requestQueue.length = 0
            throttleToLogin()
            return Promise.reject(e)
          }
        }

        return retryRequest
      } else {
        // 目前和公司后端口头约定是字符串,以防万一强制转字符串
        switch (`${code}`) {
          // code === 200 | 2 代表没有错误
          case '200':
            return data
          // code === 400001004 代表token 过期打回登录页
          case '400001004':
            throttleToLogin()
            break
          case '400':
            // 不是正确的 code
            return requestError(response)
          case '401':
            // 错误登录
            return throttleToLogin(response)
          case '600':
            // 自定义错误
            return httpRequestError(response)

          default:
            // 不是正确的 code
            return requestError(response)
        }
      }
    },
    (error) => {
      console.log(error)
      const status = error.response?.status
      switch (status) {
        // TODO 再考虑下怎么判断是跨域问题
        case undefined:
        case null:
          httpLogError(error, '网路错误或跨域')
          break
        case 400:
          httpLogError(error, '请求错误')
          break
        case 401:
          httpLogError(error, '未授权，请登录')
          break
        case 403:
          httpLogError(error, '拒绝访问')
          break
        case 404:
          httpLogError(error, `请求地址出错: ${error.response.config.url}`)
          break
        case 408:
          httpLogError(error, '请求超时')
          break
        case 500:
          httpLogError(error, '服务器内部错误')
          break
        case 501:
          httpLogError(error, '服务未实现')
          break
        case 502:
          httpLogError(error, '网关错误')
          break
        case 503:
          httpLogError(error, '服务不可用')
          break
        case 504:
          httpLogError(error, '网关超时')
          break
        case 505:
          httpLogError(error, 'HTTP版本不受支持')
          break
        case 600:
          httpRequestError(error, error.msg)
          break
        default:
          httpLogError(error, '请求错误')
          break
      }
      return Promise.reject(error)
    }
  )
  return request
}

export const service = createService()
