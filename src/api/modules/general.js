import { request } from '../request'

export class generalAPI {
  /* 获取任务列表数据 */
  static getList(params = {}) {
    return request({
      url: '/app/info/page',
      method: 'get',
      params
    })
  }

  /* 获取任务详情数据 */
  static getDetail(params = {}) {
    return request({
      url: `/app/info/detail`,
      method: 'get',
      params
    })
  }

  /* 得到字典数据 */
  static getTypeInfo(type = '', params = {}) {
    return request({
      url: `/app/api/agreement/${type}`,
      method: 'get',
      params
    })
  }

  /* 报名任务 */
  static signUp(data = {}) {
    return request({
      url: `/app/info`,
      method: 'post',
      data
    })
  }

  /* 提交任务 */
  static submitTask(data = {}) {
    return request({
      url: `/app/info`,
      method: 'put',
      data
    })
  }

  /* 我的任务 */
  static getMyTaskList(params = {}) {
    return request({
      url: `/app/info/myself/list`,
      method: 'get',
      params
    })
  }

  /* 删除记录&取消报名 */
  static deleteMyTask(id, params = {}) {
    return request({
      url: `/app/info/${id}`,
      method: 'delete',
      params
    })
  }

  /* 新增任务节点 */
  static addNode(data = {}) {
    return request({
      url: `/app/info/node`,
      method: 'post',
      data
    })
  }

  /* 修改任务节点 */
  static updateNode(data = {}) {
    return request({
      url: `/app/info/node`,
      method: 'put',
      data
    })
  }

  /* 修改用户信息 */
  static updateUserInfo(data = {}) {
    return request({
      url: `/app/user`,
      method: 'put',
      data
    })
  }

  /* 查询提现记录 */
  static getWithdrawList(params = {}) {
    return request({
      url: `/app/order/page`,
      method: 'get',
      params
    })
  }

  /* 创建提现订单 */
  static createWithdraw(data = {}) {
    return request({
      url: `/app/order/create`,
      method: 'post',
      data
    })
  }

  /* 换一个任务 */
  static changeTask(params = {}) {
    return request({
      url: `/app/info/random/detail`,
      method: 'get',
      params
    })
  }

  /* 获取排行榜 */
  static getRankList(params = {}) {
    return request({
      url: `/app/count/rank/list`,
      method: 'get',
      params
    })
  }

  /* 获取小程序手机号 */
  static getPhone(params = {}) {
    return request({
      url: `/wechat/miniprogram/phone`,
      method: 'get',
      params
    })
  }

  /* 根据类型获取字典列表 */
  static getDictList(type, params = {}) {
    return request({
      url: `/common/dict/select/${type}`,
      method: 'get',
      params
    })
  }

  /* 获取排行JSON数据 */
  static getRankJson(params = {}) {
    return request({
      url: `/app/api/rank/config`,
      method: 'get',
      params
    })
  }
}
