import { request } from '../request'

export const getBaseConfigWxInfo = (params, config = {}) =>
  request({
    url: '/base/config/wxInfo',
    method: 'get',
    params,
    ...config
  })

export const getWechatOpenid = (params, config = {}) =>
  request({
    url: '/wechat/miniprogram/openid',
    method: 'get',
    params,
    ...config
  })

export const getRandomQrcode = (data, config = {}) =>
  request({
    url: '/sale/random/qrcode',
    method: 'post',
    data,
    ...config
  })
