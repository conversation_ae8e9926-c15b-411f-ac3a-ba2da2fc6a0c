import { request } from '../request'

export class userAPI {
  static getOpenid(params = {}) {
    return request({
      url: '/wechat/miniprogram/openid',
      method: 'get',
      params
    })
  }

  static getAppOpenid(params = {}) {
    return request({
      url: '/wechat/platform/oauth2',
      method: 'get',
      params
    })
  }

  static loginIn(params = {}) {
    return request({
      url: '/app/api/auth/token',
      method: 'get',
      params
    })
  }

  static refreshToken(params = {}) {
    return request({
      url: '/app/api/auth/token',
      method: 'get',
      params
    })
  }

  static getUserInfo(params = {}) {
    return request({
      url: '/app/user',
      method: 'get',
      params
    })
  }
}
