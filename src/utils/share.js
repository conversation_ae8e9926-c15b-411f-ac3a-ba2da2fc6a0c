/*
 * @Author: abel<PERSON><PERSON> <EMAIL>
 * @Date: 2025-07-01 11:26:06
 * @LastEditors: abelethan <EMAIL>
 * @LastEditTime: 2025-07-11 09:36:00
 * @FilePath: share.js
 * @Description: 这是默认设置,可以在设置》工具》File Description中进行配置
 */
export default {
  data() {
    return {
      // 设置默认的分享参数
      // 如果页面不设置share，就触发这个默认的分享
      share: {
        title: '星愿赏',
        path: '/pages/index/index',
        desc: '',
        content: ''
      }
    }
  },
  onShareAppMessage(res) {
    return {
      title: this.share.title,
      path: this.share.path,
      imageUrl: this.share.imageUrl,
      desc: this.share.desc,
      content: this.share.content,
      success(res) {
        uni.showToast({
          title: '分享成功'
        })
      },
      fail(res) {
        uni.showToast({
          title: '分享失败',
          icon: 'none'
        })
      }
    }
  },
  onShareTimeline(res) {
    // 分享到朋友圈
    return {
      title: this.share.title,
      path: this.share.path,
      imageUrl: this.share.imageUrl,
      desc: this.share.desc,
      content: this.share.content,
      success(res) {
        uni.showToast({
          title: '分享成功'
        })
      },
      fail(res) {
        uni.showToast({
          title: '分享失败',
          icon: 'none'
        })
      }
    }
  }
}
