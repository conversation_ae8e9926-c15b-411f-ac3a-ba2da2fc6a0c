function formatNumber(num) {
  const fixed = num.toFixed(1) // 保留1位小数
  return fixed.endsWith('.0') ? fixed.slice(0, -2) : fixed
}

function formatDuration(seconds) {
  if (typeof seconds !== 'number' || seconds < 0) return '0分钟'

  const minutes = seconds / 60
  const hours = seconds / 3600
  const days = seconds / 86400

  if (minutes < 60) {
    return `${formatNumber(minutes)}分钟`
  } else if (hours < 24) {
    return `${formatNumber(hours)}小时`
  } else {
    return `${formatNumber(days)}天`
  }
}

export default formatDuration
