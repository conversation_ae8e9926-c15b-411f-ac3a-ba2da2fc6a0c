import { piniaStore } from '@/store'
import { userAPI } from '@/api/modules/user'
import { generalAPI } from '@/api/modules/general'

export const useUserStore = defineStore('user', {
  state: () => ({
    token: '',
    unionid: '',
    openid: '',
    avatarUrl: '',
    nickName: '',
    userInfo: {}
  }),
  actions: {
    login(code, openid = '', userInfo = {}) {
      return new Promise((resolve, reject) => {
        // #ifdef MP-WEIXIN
        userAPI
          .getOpenid({ code })
          .then((res) => {
            this.unionid = res.unionid
            this.openid = res.openid
            uni.setStorageSync('openid', res.openid) // 将 openid 存储到本地
            uni.setStorageSync('unionid', res.unionid) // 将 unionid 存储到本地

            userAPI
              .loginIn({
                unionid: this.unionid,
                loginType: 2,
                username: userInfo.nickName,
                avatar: userInfo.avatarUrl
              })
              .then(async (res) => {
                this.token = res.token
                uni.setStorageSync('token', res.token) // 将 token 存储到本地
                // 登录成功后获取用户信息
                await this.getUserInfo()
                this.handleUpdateUserInfo({
                  openid: this.openid,
                  unionid: this.unionid,
                  type: 'weixin'
                })
                resolve(res)
              })
              .catch(reject)
          })
          .catch(reject)
        // #endif

        // #ifdef APP-PLUS
        this.unionid = code
        this.openid = openid
        uni.setStorageSync('openid', openid) // 将 openid 存储到本地
        uni.setStorageSync('unionid', code) // 将 unionid 存储到本地
        console.log('this.unionid ~ 🚀🚀🚀🚀 ~: index ~ 行:47', this.unionid)
        console.log('this.openid ~ 🚀🚀🚀🚀 ~: index ~ 行:47', this.openid)
        userAPI
          .loginIn({
            unionid: this.unionid,
            loginType: 2,
            username: userInfo.nickName,
            avatar: userInfo.avatarUrl
          })
          .then(async (res) => {
            console.log('res ~ 🚀🚀🚀🚀 ~: index ~ 行:55', res)
            this.token = res.token
            uni.setStorageSync('token', res.token) // 将 token 存储到本地
            // 登录成功后获取用户信息
            await this.getUserInfo()
            this.handleUpdateUserInfo({ openid: this.openid, unionid: this.unionid, type: 'APP' })

            resolve(res)
          })
          .catch(reject)
        // #endif
      })
    },
    logout() {
      this.token = ''
      this.unionid = ''
      this.openid = ''
      this.userInfo = {}
      this.avatarUrl = ''
      this.nickName = ''
      uni.removeStorageSync('openid') // 清除本地存储的 openid
      uni.removeStorageSync('token') // 清除本地存储的 token
      uni.removeStorageSync('unionid') // 清除本地存储的 unionid
    },
    loginByPhone(tel) {
      return new Promise((resolve, reject) => {
        userAPI
          .loginIn({
            tel,
            loginType: 3,
            username: tel
          })
          .then((res) => {
            this.token = res.token
            uni.setStorageSync('token', res.token) // 将 token 存储到本地
            // 登录成功后获取用户信息
            this.getUserInfo()
            resolve(res)
          })
          .catch(reject)
      })
    },
    handleUpdateUserInfo(val) {
      const wxinfo = JSON.parse(this.userInfo?.wxinfo || '{}')

      wxinfo[val.type] = {
        openid: val.openid,
        unionid: val.unionid
      }
      const wxinfostr = JSON.stringify(wxinfo)
      generalAPI
        .updateUserInfo({
          wxinfo: wxinfostr,
          id: this.userInfo.id
        })
        .then((res) => {
          this.getUserInfo()
        })
        .catch((error) => {
          console.error(error)
        })
    },
    setUserInfoAvatar(val) {
      this.userInfo.avatar = val
    },
    setUserInfoTel(val) {
      this.userInfo.tel = val
    },
    setUserInfoNickName(val) {
      this.userInfo.nickName = val
    },
    async getUserInfo() {
      const token = uni.getStorageSync('token')
      if (!token) return
      const data = await userAPI.getUserInfo()
      // 将用户信息存储到 state 中
      this.userInfo = data
    }
  },
  persist: true
})

export function useOutsideUserStore() {
  return useUserStore(piniaStore)
}
