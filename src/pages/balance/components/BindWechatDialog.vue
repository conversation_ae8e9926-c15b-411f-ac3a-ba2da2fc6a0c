<template>
  <wd-popup
    v-model="visible"
    position="center"
    custom-style="border-radius:32rpx;height: 200px;width:80vw"
    round
  >
    <view class="p-4 text-center">
      <text class="text-lg">绑定微信授权</text>
      <view class="text-sm text-gray mt-2">提现需要绑定您的微信信息</view>
      <button
        class="get-user-btn mt-6"
        @click="handleClickBindWeChat"
        style="background: #07c160; color: white; border-radius: 8px"
        >一键授权绑定</button
      >
    </view>
  </wd-popup>
  <wd-toast></wd-toast>
</template>

<script setup name="BindWechatDialog">
  import { userAPI } from '@/api/modules/user'
  import { useToast } from 'wot-design-uni'
  import { generalAPI } from '@/api/modules/general'

  const toast = useToast()

  const visible = defineModel('visible')

  const userStore = useOutsideUserStore()
  /* 修改个人信息 */
  const handleUpdateUserInfo = (val) => {
    const query = {
      weixin: {
        openid: val.openid,
        unionid: val.unionid
      }
    }
    const wxinfo = JSON.stringify(query)
    generalAPI
      .updateUserInfo({
        wxinfo
      })
      .then((res) => {
        userStore.getUserInfo()
      })
      .catch((error) => {
        console.error(error)
      })
  }

  const handleClickBindWeChat = () => {
    uni.login({
      provider: 'weixin',
      success: (res) => {
        const code = res?.code
        userAPI
          .getOpenid({ code })
          .then((userInfoData) => {
            visible.value = false
            toast.success('绑定成功')
            uni.setStorageSync('openid', userInfoData.openid) // 将 openid 存储到本地
            uni.setStorageSync('unionid', userInfoData.unionid) // 将 unionid 存储到本地
            handleUpdateUserInfo(userInfoData)
          })
          .catch((error) => {
            visible.value = false
            toast.error('绑定失败，请重试')
            console.log('error ~ 🚀🚀🚀🚀 ~: BindWechatDialog ~ 行:35', error)
          })
      }
    })
  }
</script>
