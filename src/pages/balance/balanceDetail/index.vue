<template>
  <view class="z-paging-content">
    <z-paging ref="paging" v-model="dataList" @query="queryList" :auto="false">
      <!-- 需要固定在顶部不滚动的view放在slot="top"的view中，如果需要跟着滚动，则不要设置slot="top" -->
      <!-- 注意！此处的z-tabs为独立的组件，可替换为第三方的tabs，若需要使用z-tabs，请在插件市场搜索z-tabs并引入，否则会报插件找不到的错误 -->
      <template #top>
        <!-- <z-tabs :list="tabList" @change="tabsChange" /> -->
        <!-- tabs占位 -->
        <view class="h-12px pointer-events-none"></view>
      </template>

      <!-- 自定义下拉刷新view(如果use-custom-refresher为true且不设置下面的slot="refresher"，此时不用获取refresherStatus，会自动使用z-paging自带的下拉刷新view) -->

      <!-- 注意注意注意！！字节跳动小程序中自定义下拉刷新不支持slot-scope，将导致custom-refresher无法显示 -->
      <!-- 如果是字节跳动小程序，请参照sticky-demo.vue中的写法，此处使用slot-scope是为了减少data中无关变量声明，降低依赖 -->
      <template #refresher="{ refresherStatus }">
        <!-- 此处的custom-refresh为demo中自定义的组件，非z-paging的内置组件，请在实际项目中自行创建。这里插入什么view，下拉刷新就显示什么view -->
        <custom-refresher :status="refresherStatus" />
      </template>
      <!-- 自定义没有更多数据view -->
      <template #loadingMoreNoMore>
        <!-- 此处的custom-nomore为demo中自定义的组件，非z-paging的内置组件，请在实际项目中自行创建。这里插入什么view，没有更多数据就显示什么view -->
        <custom-nomore />
      </template>

      <!-- 如果希望其他view跟着页面滚动，可以放在z-paging标签内 -->
      <view class="content">
        <view v-for="item in dataList" class="wd-card-item" :key="item.id">
          <wd-card>
            <template #title>
              <view class="title">
                <view>订单号：{{ item.orderNo }}</view>
              </view>
            </template>
            <view class="flex justify-between">
              <view class="text-gray-500"> {{ item.createTime }}</view>
              <view class="text-gray-500"> <text>+</text>{{ item.amount / 100 }}元 </view>
            </view>
            <template #footer>
              <view class="flex justify-end items-center">
                <text
                  v-if="item.status === 1 || item.status === 2"
                  :class="{
                    withdrawalSuccess: item.status === 1,
                    withdrawalError: item.status === 2
                  }"
                  >{{ item.status === 1 ? '提现成功' : item.status === 2 ? '提现失败' : '' }}</text
                >
                <view v-if="item.status === 0" class="mr-1"
                  ><wd-button
                    :loading="loading"
                    @click="handleClick(item)"
                    size="small"
                    type="primary"
                    plain
                    >确认提现</wd-button
                  ></view
                >
              </view>
            </template>
          </wd-card>
        </view>
      </view>
    </z-paging>
  </view>
  <wd-message-box></wd-message-box>
  <wd-toast></wd-toast>
</template>
<script setup name="BalancePage">
  import { generalAPI } from '@/api/modules/general'
  import { useMessage, useToast } from 'wot-design-uni'
  const dataList = ref([])
  const tabIndex = ref(0)
  const paging = ref(null)

  function tabsChange(index) {
    tabIndex.value = index
    // 当切换tab或搜索时请调用组件的reload方法，请勿直接调用：queryList方法！！
    // 调用reload时参数传true则代表reload时触发下拉刷新效果，不传或false则代表取消此效果
    paging.value.reload(true)
  }

  function queryList(pageNo, pageSize) {
    // 组件加载时会自动触发此方法，因此默认页面加载时会自动触发，无需手动调用
    // 这里的pageNo和pageSize会自动计算好，直接传给服务器即可
    // 模拟请求服务器获取分页数据，请替换成自己的网络请求
    const params = {
      pageNo,
      pageSize
    }
    generalAPI
      .getWithdrawList({ current: params.pageNo, size: params.pageSize })
      .then((res) => {
        paging.value.complete(res?.records || [])
      })
      .catch(() => {
        paging.value.complete(false)
      })
  }
  onShow(() => {
    // 页面加载时自动触发查询
    queryList(1, 10)
  })
  function itemClick(item) {
    // 点击item时的处理逻辑
    uni.navigateTo({
      url: `/pages/index/taskDetail/index?id=${item.id}`
    })
  }
  const message = useMessage()
  const toast = useToast()
  const loading = ref(false)
  const handleClick = (item) => {
    loading.value = true
    generalAPI
      .createWithdraw({ id: item.id })
      .then((res) => {
        loading.value = false
        uni.requestMerchantTransfer({
          mchId: res.mchId,
          appId: res.appId,
          package: res.packageInfo,
          success: (res) => {
            // res.err_msg将在页面展示成功后返回应用时返回ok，并不代表付款成功
            console.log('success:', res)
            toast.success('提现成功')
            userStore.getUserInfo()
            paging.value.reload(true)
          },
          fail: (res) => {
            console.log('fail:', res)
            toast.error('取消提现')
          }
        })
      })
      .catch((err) => {
        console.error(err)
        loading.value = false
      })
  }
</script>

<style lang="scss" scoped>
  .content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .wd-card-item {
      width: 100%;
      margin-top: 5px;
      .title {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
      }
      .title-tip {
        color: #e83836;
        font-size: 16px;
        display: flex;
        align-items: center;
        font-weight: bold;
      }
      .withdrawalSuccess {
        color: #00b42a;
      }
      .withdrawalError {
        color: #e83836;
      }
    }
  }
</style>
