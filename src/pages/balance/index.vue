<template>
  <view class="h-full flex flex-col items-center justify-center">
    <image class="image-balance" src="@/static/balance.jpg" />
    <view class="text-center text-lg text-gray-800 my-4">我的余额</view>
    <view class="text-center text-2xl font-bold text-gray-800 my-4">
      ¥{{ userStore.userInfo.money || 0 }}
    </view>
    <view class="mt-4">
      <wd-button class="mt-6 w-3/4 mx-auto" type="primary" @click="handleClickBalance">
        提现
      </wd-button>
    </view>
    <view>
      <wd-button class="mt-6 w-3/4 mx-auto" type="info" @click="handleClickDetails">
        明细
      </wd-button>
    </view>
  </view>

  <wd-popup
    v-model="showPopup"
    position="center"
    custom-style="height: 100vh;width: 100vw;"
    closeable
  >
    <view class="p-6 w-[100vw] bg-white rounded-xl box-border">
      <view class="text-lg font-bold text-center mb-4 text-[#333]"> 提现确认 </view>

      <!-- <wd-input
        v-model="amount"
        type="number"
        placeholder="请输入提现金额"
        clearable
        border
        readonly
        custom-style="--wd-input-padding: 12px 16px;"
        @click="showKeyBoard"
      >
        <template #prefix>
          <text class="text-[#999] mr-1">¥</text>
        </template>
      </wd-input> -->
      <view class="amount-input-wrapper" @click="showKeyBoard">
        <view class="icon">¥</view>
        <view class="input-display">
          <text>{{ amount }}</text>
          <view v-if="visible" class="cursor" />
        </view>
      </view>

      <view class="text-xs text-[#f56c6c] mt-2" v-if="errorMsg">
        {{ errorMsg }}
      </view>

      <view class="mt-6 flex justify-between">
        <wd-button type="info" @click="handleClose" custom-style="width: 45%;"> 取消 </wd-button>
        <wd-button type="primary" @click="handleConfirm" custom-style="width: 45%;">
          确认提现
        </wd-button>
      </view>
      <wd-keyboard
        v-model:visible="visible"
        title="输入金额"
        extra-key="."
        close-text="完成"
        @input="onInput"
        @delete="onDelete"
      />
    </view>
  </wd-popup>
  <wd-toast></wd-toast>
  <wd-message-box selector="message-box"></wd-message-box>
  <BindWechatDialog v-model:visible="showDialog"></BindWechatDialog>
</template>

<script setup name="BalancePage">
  import { generalAPI } from '../../api/modules/general'
  import { useToast, useMessage } from 'wot-design-uni'
  import BindWechatDialog from './components/BindWechatDialog.vue'

  const toast = useToast()
  const message = useMessage('message-box')
  const userStore = useOutsideUserStore()

  const visible = ref(false)

  function showKeyBoard() {
    visible.value = true
  }
  const inputStr = ref('')

  function validate(val) {
    if (!/^\d+(\.\d{0,2})?$/.test(val)) {
      return false
    }
    errorMsg.value = ''
    return true
  }
  function onInput(key) {
    if (key === '.' && inputStr.value.includes('.')) return

    inputStr.value += key
    if (validate(inputStr.value)) {
      amount.value = inputStr.value
    } else {
      inputStr.value = inputStr.value.slice(0, -1)
    }
  }

  // 删除键
  function onDelete() {
    inputStr.value = inputStr.value.slice(0, -1)
    amount.value = inputStr.value
  }

  const handleClickDetails = () => {
    uni.navigateTo({
      url: '/balanceDetail'
    })
  }

  /* 提现 */
  const showPopup = ref(false)
  const amount = ref('')
  const errorMsg = ref('')
  const handleClose = () => {
    showPopup.value = false
    amount.value = ''
    errorMsg.value = ''
  }

  const showDialog = ref(false)
  const handleClickBalance = () => {
    const wxinfo = JSON.parse(userStore.userInfo?.wxinfo || '{}')
    const openid = wxinfo.weixin?.openid
    const unionid = wxinfo.weixin?.unionid

    if (!openid || !unionid) {
      showDialog.value = true
      return
    } else {
      uni.setStorageSync('openid', openid) // 将 openid 存储到本地
      uni.setStorageSync('unionid', unionid) // 将 unionid 存储到本地
    }
    showPopup.value = true
    amount.value = ''
    errorMsg.value = ''
  }

  const handleConfirm = () => {
    const val = parseFloat(amount.value)

    if (!amount.value || isNaN(val)) {
      errorMsg.value = '请输入提现金额'
      return
    }
    const valid = /^\d+(\.\d{0,2})?$/.test(val)
    if (!valid) {
      errorMsg.value = '请输入合法的金额（最多两位小数）'
      return
    }
    if (val > userStore.userInfo.money) {
      errorMsg.value = '输入金额不能超过余额'
      return
    }
    if (val < 0.3) {
      errorMsg.value = '提现金额不能低于 0.3 元'
      return
    }

    if (val > 200) {
      errorMsg.value = '提现金额不能超过 200 元'
      return
    }

    // 发起提现逻辑
    const openid = uni.getStorageSync('openid')
    const query = {
      amount: val * 100,
      openid
    }
    /* #ifdef MP-WEIXIN */
    query.wxEnv = 2
    /* #endif */

    /* #ifdef APP-PLUS */
    query.wxEnv = 1
    /* #endif */

    generalAPI
      .createWithdraw(query)
      .then((res) => {
        toast.success('提现申请已提交')
        userStore.getUserInfo()
        uni.requestMerchantTransfer({
          mchId: res.mchId,
          appId: res.appId,
          package: res.packageInfo,
          success: (res) => {
            // res.err_msg将在页面展示成功后返回应用时返回ok，并不代表付款成功
            toast.success('提现成功')
            userStore.getUserInfo()
            handleClose()
          },
          fail: (res) => {
            console.log('fail:', res)
            toast.error('取消提现')
          }
        })
      })
      .catch((err) => {
        console.error(err)
        errorMsg.value = err.msg ? err.msg : '提现失败，请稍后再试'
      })
  }
</script>

<style lang="scss" scoped>
  .image-balance {
    width: 80px;
    height: 80px;
    margin: 0 auto;
    display: block;
    margin-top: -100px;
    border-radius: 100px;
  }
  .amount-input-wrapper {
    display: flex;
    align-items: center;
    padding: 12rpx 24rpx;
    border-bottom: 2rpx solid #ccc;
    border-radius: 12rpx;
    background-color: #f8f8f8;
    font-size: 34rpx;
    position: relative;
  }
  .icon {
    color: #666;
    margin-right: 12rpx;
  }
  .input-display {
    flex: 1;
    display: flex;
    align-items: center;
    min-height: 50rpx;
    color: #333;
  }
  .cursor {
    width: 4rpx;
    height: 1.2em;
    background-color: #333;
    margin-left: 6rpx;
    animation: blink 1s step-end infinite;
  }
  @keyframes blink {
    50% {
      opacity: 0;
    }
  }
</style>
