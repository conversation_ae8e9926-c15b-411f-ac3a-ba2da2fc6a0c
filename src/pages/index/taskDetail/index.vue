<template>
  <view class="content">
    <view class="header"></view>
    <view class="header-card">
      <view class="header-card-top">
        <view class="header-card-top-left">
          <text class="title">{{ detail.title }}</text>
          <text class="task-num">任务编号：{{ detail.taskNo }}</text>
        </view>
        <view class="header-card-top-right">
          <text class="header-card-top-right-text"
            ><text class="amount-icon">¥</text>{{ detail.money }}</text
          >
        </view>
      </view>
      <view class="header-card-center">
        <view class="tag-item">剩余{{ detail.num - detail.doneNum }}单</view>
      </view>
      <view class="header-card-bottom">
        <view>
          <view class="font-weight-bold">{{ formatDuration(detail.avgAuditTime) }}</view>
          <view>人均用时</view>
        </view>
        <wd-divider vertical />
        <view>
          <view class="font-weight-bold">{{ formatDuration(detail.avgSubmitTime) }}</view>
          <view>平均审核</view>
        </view>
        <wd-divider vertical />
        <view>
          <view class="font-weight-bold">{{ detail.doneNum }}</view>
          <view>已完成数</view>
        </view>
        <wd-divider vertical />
        <view>
          <view class="font-weight-bold">{{ formatDuration(detail.timeLimit) }}</view>
          <view>做单限时</view>
        </view>
      </view>
    </view>
    <view class="flex-1 flex items-center justify-center center-content">
      <view class="center-content-main">
        <view class="main-card">
          <view class="main-title">官方提示</view>
          <view class="main-content">
            <uv-parse :content="tipStr"></uv-parse>
          </view>
        </view>
        <view class="main-card">
          <view class="main-title">任务描述</view>
          <view class="main-content">{{ detail.describes }}</view>
        </view>
        <view class="main-card">
          <view class="main-title">任务步骤</view>
          <view class="main-content">
            <view class="step-item" v-for="(step, index) in detail.nodeList" :key="step.id">
              <view class="flex items-center">
                <view class="num">{{ index + 1 }}</view>
                <text selectable="true" class="text-ellipsis">{{ step.title }}</text>
              </view>
              <view class="step-content">
                <canvas
                  canvas-id="watermarkCanvas"
                  id="watermarkCanvas"
                  class="hidden-canvas"
                  style="
                    width: 300px;
                    height: 300px;
                    position: absolute;
                    top: -9999px;
                    left: -9999px;
                    z-index: -9999;
                    opacity: 0;
                  "
                />
                <!-- 文本 -->
                <text selectable="true" v-if="step.type === 1 && !isEmpty(step.content)">{{
                  step.content
                }}</text>

                <!-- 图片 -->
                <image
                  v-else-if="step.type === 2 && !isEmpty(step.content)"
                  :src="step.content"
                  mode="widthFix"
                  style="width: 50%"
                  @click="previewImage(step)"
                />

                <!-- 视频 -->
                <video
                  v-else-if="step.type === 3 && !isEmpty(step.content)"
                  :src="step.content"
                  controls
                  autoplay
                  style="width: 100%"
                />

                <!-- 链接 -->
                <view
                  v-else-if="step.type === 4 && !isEmpty(step.content)"
                  @click="openLink(step.content)"
                >
                  <text style="color: #007aff">🔗 点击复制链接</text>
                </view>

                <!-- 兜底 -->
                <view v-if="!isSignUp && step.isSubmit === 1" class="step-content-right">
                  <wd-upload
                    v-if="step.type === 2 || step.type === 3 || step.type === 4"
                    :limit="1"
                    v-model:file-list="step.taskFileList"
                    image-mode="aspectFill"
                    :disabled="isBeenSubmitTask"
                    :before-remove="beforeRemove"
                    :upload-method="
                      (file, formData, options) => customUpload(file, formData, options, step)
                    "
                  ></wd-upload>
                  <wd-textarea
                    v-if="step.type === 1"
                    v-model="step.taskContent"
                    :disabled="isBeenSubmitTask"
                    placeholder="请输入任务内容"
                    auto-height
                    custom-class="my-2"
                  />
                  <wd-button
                    @click="handleClickSave(step)"
                    v-if="step.type === 1 && !isBeenSubmitTask"
                    type="primary"
                    block
                    >保存</wd-button
                  >
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
      <view class="center-content-footer">
        <view class="flex items-center justify-between w-full px-2">
          <view @click="handleChangeTask" class="change-btn">
            <wd-icon name="refresh" size="22px"></wd-icon><text>换一个</text>
          </view>
          <wd-button
            v-if="!isBeenSubmitTask"
            @click="signUp"
            :disabled="countdownText !== '任务已超时' && !isCanSubmitTask"
            custom-class="sign-btn"
          >
            <text v-if="countdownText !== '任务已超时'" class="time">{{ countdownText }}</text>
            <text class="btn-text">{{
              isSignUp ? '我要报名' : countdownText === '任务已超时' ? '重新报名' : `提交任务`
            }}</text>
          </wd-button>
        </view>
      </view>
    </view>
  </view>
  <wd-message-box selector="wd-message-box-slot"> </wd-message-box>
  <wd-toast selector="wd-toast-box-slot" />
</template>

<script setup name="TaskDetail">
  import { generalAPI } from '../../../api/modules/general'
  import { isEmpty } from '../../../utils'
  import formatDuration from '../../../utils/time'
  import { useMessage, useToast } from 'wot-design-uni'

  const userStore = useOutsideUserStore()
  const userInfo = computed(() => userStore.userInfo)

  const tipList = ref([])
  const getTip = async () => {
    try {
      const data = await generalAPI.getTypeInfo(4)
      tipList.value = data || []
    } catch (error) {
      console.error(error)
    }
  }
  const tipStr = computed(() => {
    return tipList.value.map((item) => item.content).join('；')
  })
  const detail = ref({})
  const getTaskDetail = async (id) => {
    try {
      const data = await generalAPI.getDetail({ id, userId: userInfo.value?.id })
      // 处理任务详情数据
      detail.value = data || {}
      if (detail.value.nodeList && detail.value.nodeList.length > 0) {
        detail.value.nodeList.forEach((item) => {
          const infoUserNode = findInfoUserNodeByNodeId(item.id)
          if (infoUserNode) {
            item.taskFileList = infoUserNode.fileList || []
            item.infoUserNodeId = infoUserNode.id
            if (item.type === 1) {
              item.taskContent = infoUserNode.content || ''
            } else if (item.type === 2 || item.type === 3) {
              item.taskFileList = [{ url: infoUserNode.content }]
            }
          } else {
            item.infoUserNodeId = null
            if (item.type === 1) {
              item.taskContent = ''
            } else if (item.type === 2 || item.type === 3) {
              item.taskFileList = []
            }
          }
        })
      }
      updateCountdown()
      timer = setInterval(updateCountdown, 1000)
    } catch (error) {
      console.error(error)
    }
  }

  const findInfoUserNodeByNodeId = (nodeId) => {
    return detail.value.infoUser?.nodeList?.find((item) => item.nodeId === nodeId)
  }

  /* 是否报名 */
  const isSignUp = computed(() => {
    return !detail.value?.infoUser
  })

  /* 是否已提交任务 */
  const isBeenSubmitTask = computed(() => {
    return detail.value?.infoUser?.auditStatus === 1 && detail.value?.infoUser?.auditStatus === 1
  })

  const openLink = (url) => {
    uni.setClipboardData({
      data: url,
      success() {
        toast.success('链接已复制')
      },
      fail() {
        toast.error('复制链接失败')
      }
    })
  }
  const signUp = () => {
    if (countdownText.value !== '任务已超时' && !isSignUp.value) {
      generalAPI
        .submitTask({
          id: detail.value.infoUser.id
        })
        .then((data) => {
          toast.success('提交任务成功')
          getTaskDetail(taskId.value)
        })
        .catch((error) => {
          toast.error(error.msg ? error.msg : '提交任务失败，请稍后再试')
        })
    } else {
      generalAPI
        .signUp({
          taskId: Number(taskId.value)
        })
        .then((res) => {
          if (res !== undefined) {
            toast.success('报名成功')

            getTaskDetail(taskId.value)
          }
        })
        .catch((error) => {
          toast.error(error.msg ? error.msg : '报名失败，请稍后再试')
        })
    }
  }

  const handleChangeTask = async () => {
    try {
      const data = await generalAPI.changeTask({ id: taskId.value, userId: userInfo.value?.id })
      if (data) {
        taskId.value = data.id
        toast.success('换单成功')
        getTaskDetail(taskId.value)
      } else {
        toast.info('没有更多任务了')
      }
    } catch (error) {
      console.error('Error changing task:', error)
    }
  }

  const taskId = ref(null)
  onLoad((options) => {
    const { id } = options
    taskId.value = id
    // 获取任务详情
    getTaskDetail(id)
  })

  onShow(() => {
    getTip()
  })

  const countdownText = ref('')

  // 倒计时核心逻辑
  let timer = null

  function formatTime(seconds) {
    const h = String(Math.floor(seconds / 3600)).padStart(2, '0')
    const m = String(Math.floor((seconds % 3600) / 60)).padStart(2, '0')
    const s = String(seconds % 60).padStart(2, '0')
    return `${h}:${m}:${s}`
  }

  function updateCountdown() {
    const startTime = Math.floor(
      new Date(detail.value?.infoUser?.createTime.replace(/-/g, '/')).getTime() / 1000
    )
    const now = Math.floor(Date.now() / 1000)
    const endTime = startTime + detail.value.timeLimit
    const remain = endTime - now
    if (remain > 0) {
      countdownText.value = formatTime(remain)
    } else {
      countdownText.value = '任务已超时'
      clearInterval(timer)
      timer = null
    }
  }

  const isCanSubmitTask = computed(() => {
    return (
      detail.value?.infoUser?.nodeList?.length ===
      detail.value?.nodeList?.filter((item) => item.isSubmit === 1).length
    )
  })

  const messageBox = useMessage('wd-message-box-slot')
  const toast = useToast('wd-toast-box-slot')

  const beforeRemove = ({ file, fileList, resolve }) => {
    messageBox
      .confirm({
        msg: '是否删除',
        title: '提示'
      })
      .then(() => {
        toast.success('删除成功')
        resolve(true)
      })
      .catch(() => {
        toast.show('取消删除操作')
      })
  }

  const customUpload = async (file, formData, options, step) => {
    const uploadTask = uni.uploadFile({
      url: `${import.meta.env.VITE_APP_API_BASEURL}/common/file/oss/upload`,
      header: options.header,
      name: options.name,
      fileName: options.name,
      fileType: options.fileType,
      formData,
      filePath: file.url,
      success(res) {
        if (res.statusCode === options.statusCode) {
          // 设置上传成功
          options.onSuccess(res, file, formData)
          const fileUrl = JSON.parse(file.response).data.fileUrl
          if (fileUrl) {
            const query = { content: fileUrl, taskUserId: detail.value?.infoUser?.id }
            query.nodeId = step.id
            generalAPI
              .addNode(query)
              .then(() => {
                toast.success('文件上传成功')
                getTaskDetail(taskId.value)
              })
              .catch((error) => {
                toast.error(error.msg ? error.msg : '文件上传失败，请稍后再试')
              })
          }
        } else {
          // 设置上传失败
          options.onError({ ...res, errMsg: res.errMsg || '' }, file, formData)
        }
      },
      fail(err) {
        // 设置上传失败
        options.onError(err, file, formData)
      }
    })
    // 设置当前文件加载的百分比
    uploadTask.onProgressUpdate((res) => {
      options.onProgress(res, file)
    })
  }

  const handleClickSave = async (step) => {
    if (step.type === 1 && !step.taskContent) {
      toast.info('请输入任务内容')
      return
    }
    try {
      await generalAPI.addNode({
        content: step.taskContent,
        taskUserId: detail.value?.infoUser?.id,
        nodeId: step.id
      })
      toast.success('保存成功')
      getTaskDetail(taskId.value)
    } catch (error) {
      toast.error(error.msg ? error.msg : '保存失败，请稍后再试')
    }
  }
  const watermarkText = '示例图' // 你的水印文本
  const previewImage = (item) => {
    uni.showLoading({
      title: '正在加载图片...',
      mask: true
    })
    uni.getImageInfo({
      src: item.content, // 原图地址（可能是网络图）
      success: (info) => {
        const ctx = uni.createCanvasContext('watermarkCanvas')

        // 使用本地临时路径绘图
        ctx.drawImage(info.path, 0, 0, 300, 300)

        ctx.setFontSize(14)
        ctx.setFillStyle('rgba(255,255,255,0.7)')
        ctx.fillText(watermarkText, 180, 280)

        ctx.draw(false, () => {
          uni.canvasToTempFilePath({
            canvasId: 'watermarkCanvas',
            success: (res) => {
              uni.hideLoading()
              const watermarkedPath = res.tempFilePath
              uni.previewImage({
                current: watermarkedPath,
                urls: [watermarkedPath]
              })
            },
            fail: (err) => {
              console.error('生成带水印图失败', err)
            }
          })
        })
      },
      fail: (err) => {
        console.error('获取图片信息失败', err)
      }
    })
  }

  onUnmounted(() => {
    if (timer) clearInterval(timer)
  })
</script>

<style lang="scss" scoped>
  .content {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .header {
      position: absolute;
      z-index: -1;
      width: 100%;
      height: 100px;
      background-color: #4574f0;
      border-radius: 0 0 20px 20px;
    }

    .header-card {
      position: absolute;
      top: 0;
      width: calc(100vw - 20px);
      height: 160px;
      background-color: #fff;
      border-radius: 10px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);

      .header-card-top {
        display: flex;
        justify-content: space-between;
        padding: 10px 10px 5px;

        .header-card-top-left {
          display: flex;
          flex-direction: column;

          .title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
          }

          .task-num {
            font-size: 14px;
            color: #b3b3b3;
          }
        }

        .header-card-top-right {
          font-size: 24px;
          color: #ff5722;

          .amount-icon {
            font-size: 16px;
          }
        }
      }

      .header-card-center {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        padding: 0 10px;

        .tag-item {
          padding: 0 10px;
          font-size: 14px;
          color: #fff;
          background-color: #db4d58;
          border-radius: 5px;
        }
      }

      .header-card-bottom {
        display: flex;
        justify-content: space-around;
        padding: 10px;

        view {
          display: flex;
          flex-direction: column;
          align-items: center;
          color: #666;

          .font-weight-bold {
            font-size: 14px;
            font-weight: bold;
            color: #333;
          }

          view {
            font-size: 12px;
          }
        }
      }
    }

    .center-content {
      position: absolute;
      top: 160px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: calc(100vh - 160px);

      .center-content-main {
        flex: 1;
        padding-top: 10px;
        padding-left: 10px;
        /* stylelint-disable-next-line order/properties-order */
        padding-right: 10px;
        margin-bottom: 10px;
        font-size: 24px;
        color: #333;
        overflow-y: scroll;

        .main-card {
          background-color: #ffffff;
          border-radius: 10px;
          padding: 10px;
          box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
          margin-bottom: 10px;

          .main-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
          }

          .main-content {
            font-size: 14px;
            color: #666;
            line-height: 1.5;

            .step-item {
              margin-bottom: 10px;

              .step-content {
                display: flex;
                justify-content: space-between;
                flex-direction: column;
                margin-top: 10px;
                position: relative;

                .step-content-right {
                  margin-top: 10px;
                }
              }
            }

            .num {
              display: inline-block;
              width: 20px;
              height: 20px;
              line-height: 20px;
              text-align: center;
              border-radius: 50%;
              background-color: #4574f0;
              color: #fff;
              margin-right: 10px;
            }

            .text-ellipsis {
              word-break: break-word;
              overflow-wrap: break-word;
              white-space: normal;
              max-width: calc(100% - 30px);
            }
          }
        }
      }

      .center-content-footer {
        display: flex;
        align-items: center;
        width: 100%;
        height: 60px;
        font-size: 16px;
        color: #333;
        background-color: #ffffff;

        :deep() .sign-btn {
          padding: 10px 20px;
          width: 60%;
          background-color: #4574f0;
          color: #fff;
          border-radius: 5px;
          text-align: center;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 16px;

          .time {
            font-weight: bold;
            font-size: 12px;
            margin-right: 5px;
          }

          .btn-text {
            font-weight: bold;
            font-size: 14px;
          }
        }

        .change-btn {
          padding: 10px 20px;
          color: #767676;
          border-radius: 5px;
          text-align: center;
          display: flex;
          align-items: center;
          justify-content: space-around;
          flex-direction: column;
          font-size: 14px;
        }
      }
    }
  }
</style>
