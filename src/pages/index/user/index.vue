<script setup>
  import { useMessage, useToast } from 'wot-design-uni'
  import { generalAPI } from '../../../api/modules/general'

  const version = '1.0.0'
  const userStore = useOutsideUserStore()
  const router = useRouter()
  const isLogin = computed(() => !!token.value || false)
  const userInfo = computed(() => userStore.userInfo)

  const systemItems = computed(() => [
    {
      icon: 'i-carbon-edge-cluster',
      text: '邀请码',
      path: '/inviteCode'
    },
    {
      icon: 'i-carbon-certificate',
      text: '资质证书',
      path: '/certificate'
    },
    {
      icon: 'i-carbon-carbon-for-ibm-product',
      text: '我的任务',
      path: '/myTask'
    },
    {
      icon: 'i-carbon-document',
      text: '隐私协议',
      path: '/Protocol',
      query: {
        argumentType: 'privacy'
      }
    },
    {
      icon: 'i-carbon-network-time-protocol',
      text: '用户协议',
      path: '/Protocol',
      query: {
        argumentType: 'product'
      }
    }
    // {
    //   icon: 'i-carbon-qr-code',
    //   text: '二维码',
    //   path: '/qrcode'
    // }
  ])
  const messageBox = useMessage('wd-message-box-slot')
  const messageUpload = useMessage('wd-message-upload')
  const inviteCode = ref('')

  function handleMenuItemClick(item) {
    if (item.path === '/inviteCode') {
      if (!isLogin.value) {
        handleLogin()
        return
      }
      if (userStore.userInfo.inviteCode) {
        inviteCode.value = userStore.userInfo.inviteCode
      }
      messageBox
        .prompt({
          title: '请输入邀请码',
          inputValue: inviteCode.value
        })
        .then((resp) => {
          const query = {
            inviteCode: resp.value,
            id: userStore.userInfo.id
          }
          generalAPI
            .updateUserInfo(query)
            .then((res) => {
              userStore.getUserInfo()
              toast.success('邀请码设置成功')
            })
            .catch((error) => {
              toast.error(error.msg ? error.msg : '邀请码不能使用')
            })
        })
        .catch((error) => {
          console.log(error)
        })
    } else if (item.path === '/certificate') {
      if (!isLogin.value) {
        handleLogin()
        return
      }
      if (userStore.userInfo.cert) {
        fileList.value = [{ url: userStore.userInfo.cert }]
      }
      messageUpload
        .confirm({
          title: '资质证书'
        })
        .then(() => {
          console.log('fileList.value ~ 🚀🚀🚀🚀 ~: index ~ 行:98', fileList.value[0])
          if (fileList.value.length === 0) {
            toast.error('请先选择资质证书')
          } else {
            const fileUrl = JSON.parse(fileList.value[0].response).data.fileUrl
            const query = {
              cert: fileUrl,
              id: userStore.userInfo.id
            }
            generalAPI
              .updateUserInfo(query)
              .then((res) => {
                userStore.getUserInfo()
                toast.success('资质证书设置成功')
              })
              .catch((error) => {
                toast.error(error.msg ? error.msg : '资质证书设置失败')
              })
          }
        })
        .catch((error) => {
          console.log(error)
        })
    } else {
      router.push({
        path: item.path,
        query: item.query || {}
      })
    }
  }

  const handleClickBalance = () => {
    router.push({
      path: '/balance'
    })
  }

  function handleLogin() {
    if (isLogin.value) {
      /* #ifdef MP-WEIXIN */
      router.push({
        path: '/personal'
      })
      /* #endif */

      return false
    }

    router.push({
      path: '/login'
    })
  }

  function onEnterpriseClick() {
    window.open(userInfo.value.url)
  }
  const toast = useToast('wd-toast-box-slot')

  function handleLogout() {
    uni.showModal({
      title: '提示',
      content: '确定要退出登录吗?',
      showCancel: true,
      confirmText: '确定',
      cancelText: '取消',
      success: async function (res) {
        if (res.confirm) {
          await userStore.logout()
          toast.success('退出登录成功')
          router.replace({
            path: '/login'
          })
        } else if (res.cancel) {
          console.log('用户点击取消')
        }
      }
    })
  }
  const token = ref('')
  onShow(() => {
    // 页面加载时自动触发查询
    userStore.getUserInfo()
    token.value = uni.getStorageSync('token')
  })

  /* 上传资质 */
  const fileList = ref([])

  const customUpload = async (file, formData, options) => {
    const uploadTask = uni.uploadFile({
      url: `${import.meta.env.VITE_APP_API_BASEURL}/common/file/oss/upload`,
      header: options.header,
      name: options.name,
      fileName: options.name,
      fileType: options.fileType,
      formData,
      filePath: file.url,
      success(res) {
        if (res.statusCode === options.statusCode) {
          // 设置上传成功
          options.onSuccess(res, file, formData)
        } else {
          // 设置上传失败
          options.onError({ ...res, errMsg: res.errMsg || '' }, file, formData)
        }
      },
      fail(err) {
        // 设置上传失败
        options.onError(err, file, formData)
      }
    })
    // 设置当前文件加载的百分比
    uploadTask.onProgressUpdate((res) => {
      options.onProgress(res, file)
    })
  }
</script>

<template>
  <view class="h-full flex flex-col">
    <view class="relative overflow-hidden">
      <view class="absolute inset-0 bg-blue-500"></view>

      <view
        class="absolute h-42 w-42 rounded-full bg-white opacity-10 -right-10 -top-10 z-1"
      ></view>
      <view
        class="absolute bottom-0 right-20 h-20 w-20 rounded-full bg-white opacity-10 z-1"
      ></view>

      <view class="h-[--safe-top]"></view>

      <view
        class="relative flex items-center px-6 pb-12 pt-12 bg-blue-500"
        hover-class="opacity-90"
        @click="handleLogin"
      >
        <view class="h-18 w-18 overflow-hidden border-2 border-white/30 rounded-full shadow-lg">
          <image
            :src="userInfo.avatar ? userInfo.avatar : '/static/avatar.jpeg'"
            alt="用户头像"
            class="h-full w-full"
          />
        </view>

        <view class="ml-4 flex-1">
          <view v-if="isLogin" class="text-xl text-white font-bold flex items-center">
            <view class="text-xl text-white font-medium"> {{ userInfo.nickName }} </view>
            <view class="ml-2 rounded-full bg-white/20 px-3 py-1 text-xs text-white">
              {{ userInfo.grade }}星
            </view>
          </view>
          <view v-else class="flex items-center">
            <view class="text-xl text-white font-medium"> 立即登录 </view>
            <view class="ml-2 rounded-full bg-white/20 px-3 py-1 text-xs text-white"> 未登录 </view>
          </view>
        </view>

        <!-- <view v-if="isLogin" class="flex items-center text-white/70">
          <view>编辑个人资料</view>
          <view class="i-carbon-chevron-right size-6"></view>
        </view> -->
      </view>
    </view>
    <view class="mx-3 mt-3 overflow-hidden rounded-xl shadow-sm">
      <view
        class="flex items-center bg-white px-5 py-4 transition-colors duration-200 active:bg-gray-50"
        @click="handleClickBalance"
      >
        <view class="w-10 flex flex-none items-center justify-center text-gray-500">
          <view class="size-6 text-blue-500 i-carbon-money"></view>
        </view>
        <view class="flex-1 text-gray-700 font-medium"> 余额： </view>
        <view class="text-black-400 font-bold"> ¥{{ userStore.userInfo.money || 0 }} </view>
      </view>
    </view>
    <view class="mx-3 mt-3 overflow-hidden rounded-xl shadow-sm">
      <view
        v-for="(item, index) of systemItems"
        :key="index"
        :class="[index !== systemItems.length - 1 ? 'border-b border-gray-100' : '']"
        class="flex items-center bg-white px-5 py-4 transition-colors duration-200 active:bg-gray-50"
        hover-class="bg-gray-50"
        @click="handleMenuItemClick(item)"
      >
        <view class="w-10 flex flex-none items-center justify-center text-gray-500">
          <view class="size-6 text-blue-500" :class="item.icon"></view>
        </view>

        <view class="flex-1 text-gray-700 font-medium">
          {{ item.text }}
        </view>
        <view class="text-gray-400">
          {{ item.path === '/inviteCode' ? userInfo.expiryTime : '' }}
          <text v-if="item.path === '/certificate' && isLogin">{{
            !userInfo.cert
              ? '未上传'
              : userInfo.certPass === 0
                ? '未通过'
                : userInfo.certPass === 1
                  ? '审核中'
                  : '审核通过'
          }}</text>
          <view class="i-carbon-chevron-right size-5"></view>
        </view>
      </view>
    </view>

    <view v-if="isLogin" class="mb-8 mt-auto px-5">
      <!-- <button
        class="w-full bg-red-500 py-3 text-gray-50 font-medium transition-colors duration-200 !rounded-lg"
        hover-class="bg-red-700"
        @click="handleLogout"
      >
        退出登录
      </button> -->
    </view>

    <view v-else class="mb-6 mt-auto text-center text-xs text-gray-400">
      <view>
        Supported by
        <text class="text-blue-500 underline active:text-blue-700" @click="onEnterpriseClick">
          {{ userInfo.name }}
        </text>
        v{{ version }}
      </view>
    </view>
  </view>
  <wd-message-box selector="wd-message-box-slot"> </wd-message-box>
  <wd-toast selector="wd-toast-box-slot"> </wd-toast>
  <wd-message-box selector="wd-message-upload">
    <view class="flex align-center justify-center p-4">
      <wd-upload
        :limit="1"
        v-model:file-list="fileList"
        image-mode="aspectFill"
        :upload-method="(file, formData, options) => customUpload(file, formData, options)"
      ></wd-upload>
    </view>
  </wd-message-box>
</template>
