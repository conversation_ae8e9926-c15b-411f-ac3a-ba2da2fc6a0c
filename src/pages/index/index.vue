<template>
  <view class="z-paging-content">
    <z-paging ref="paging" v-model="dataList" @query="queryList">
      <!-- 需要固定在顶部不滚动的view放在slot="top"的view中，如果需要跟着滚动，则不要设置slot="top" -->
      <!-- 注意！此处的z-tabs为独立的组件，可替换为第三方的tabs，若需要使用z-tabs，请在插件市场搜索z-tabs并引入，否则会报插件找不到的错误 -->
      <template #top>
        <!-- <z-tabs :list="tabList" @change="tabsChange" /> -->
        <!-- tabs占位 -->
        <!-- <view class="h-12px pointer-events-none"></view> -->
        <wd-drop-menu custom-class="mb-2">
          <wd-drop-menu-item v-model="orderBy" :options="option2" />
          <wd-drop-menu-item title="筛选" ref="dropMenu">
            <view class="p-4 border-box">
              <view class="my-4 text-gray-500">任务类型</view>
              <view class="flex justify-between">
                <wd-radio-group shape="button" v-model="taskType">
                  <wd-radio custom-class="m-2" value="all">全部</wd-radio>
                  <wd-radio
                    custom-class="m-2"
                    v-for="item in taskTypeList"
                    :key="item.id"
                    :value="item.value"
                    >{{ item.label }}</wd-radio
                  >
                </wd-radio-group>
              </view>
              <view class="my-4 text-gray-500">金额区间</view>
              <view class="price-range-container">
                <input
                  v-model.number="minPrice"
                  type="number"
                  placeholder="最低金额"
                  :clearable="true"
                  @input="(val) => onInput('min', val)"
                />
                <text class="split-text">-</text>
                <input
                  v-model.number="maxPrice"
                  type="number"
                  placeholder="最高金额"
                  :clearable="true"
                  @input="(val) => onInput('max', val)"
                />
              </view>

              <view class="flex justify-end mt-4">
                <wd-button custom-class="mr-2" type="info" @click="handleReset" block suck
                  >重置</wd-button
                >
                <wd-button @click="handleConfirm" block suck>确定</wd-button>
              </view>
            </view>
          </wd-drop-menu-item>
        </wd-drop-menu>
      </template>

      <!-- 自定义下拉刷新view(如果use-custom-refresher为true且不设置下面的slot="refresher"，此时不用获取refresherStatus，会自动使用z-paging自带的下拉刷新view) -->

      <!-- 注意注意注意！！字节跳动小程序中自定义下拉刷新不支持slot-scope，将导致custom-refresher无法显示 -->
      <!-- 如果是字节跳动小程序，请参照sticky-demo.vue中的写法，此处使用slot-scope是为了减少data中无关变量声明，降低依赖 -->
      <template #refresher="{ refresherStatus }">
        <!-- 此处的custom-refresh为demo中自定义的组件，非z-paging的内置组件，请在实际项目中自行创建。这里插入什么view，下拉刷新就显示什么view -->
        <custom-refresher :status="refresherStatus" />
      </template>
      <!-- 自定义没有更多数据view -->
      <template #loadingMoreNoMore>
        <!-- 此处的custom-nomore为demo中自定义的组件，非z-paging的内置组件，请在实际项目中自行创建。这里插入什么view，没有更多数据就显示什么view -->
        <custom-nomore />
      </template>

      <!-- 如果希望其他view跟着页面滚动，可以放在z-paging标签内 -->
      <view class="content">
        <view v-for="item in dataList" class="wd-card-item" :key="item.id" @click="itemClick(item)">
          <wd-card>
            <template #title>
              <view class="title">
                <view class="flex items-center"
                  ><wd-tag type="success" mark>官方</wd-tag
                  ><text class="ml-1">{{ item.title }}</text></view
                >
                <view class="title-tip">
                  <wd-rate v-model="item.grade" readonly />
                </view>
              </view>
            </template>
            <view> {{ item.describes }}</view>
            <view v-if="item.tags" class="flex justify-end"
              ><wd-tag
                v-for="(tagItem, tagIndex) in item.tags.split(',')"
                :key="tagIndex"
                custom-class="mx-1"
                type="warning"
                mark
                >{{ tagItem }}</wd-tag
              ></view
            >
            <template #footer>
              <view class="flex justify-between items-center">
                <view class="text-gray-500 text-xs flex items-center"
                  ><view class="earned">{{ item.doneNum }}</view
                  >人已赚
                  <view class="remaining"
                    >剩余<text class="ml-[1px] text-red">{{ item.num - item.doneNum }}</text>
                    个</view
                  ></view
                >
                <view class="num">+{{ item.money }} <text class="text-xs">元</text></view>
              </view>
            </template>
          </wd-card>
        </view>
      </view>
    </z-paging>
  </view>
</template>
<script setup>
  import { generalAPI } from '../../api/modules/general'

  const option2 = ref([
    { label: '全部', value: 'all' },
    { label: '金额从低到高', value: 0 },
    { label: '金额从高到低', value: 1 }
  ])
  const orderBy = ref('all')

  const taskType = ref('all')

  /* 价格区间 */
  const minPrice = ref(null)
  const maxPrice = ref(null)

  function onInput(type, val) {
    console.log('val ~ 🚀🚀🚀🚀 ~: index ~ 行:134', val)
    const filtered = val.detail.value.replace(/[^\d]/g, '') // 仅保留数字
    if (type === 'min') {
      minPrice.value = parseFloat(filtered)
    } else {
      maxPrice.value = parseFloat(filtered)
    }
    console.log(' ~ 🚀🚀🚀🚀 ~: index ~ 行:140', minPrice.value, maxPrice.value)
  }
  /* 价格区间 */

  const dataList = ref([])
  const tabIndex = ref(0)
  const paging = ref(null)

  function tabsChange(index) {
    tabIndex.value = index
    // 当切换tab或搜索时请调用组件的reload方法，请勿直接调用：queryList方法！！
    // 调用reload时参数传true则代表reload时触发下拉刷新效果，不传或false则代表取消此效果
    paging.value.reload(true)
  }

  function queryList(pageNo, pageSize) {
    // 组件加载时会自动触发此方法，因此默认页面加载时会自动触发，无需手动调用
    // 这里的pageNo和pageSize会自动计算好，直接传给服务器即可
    // 模拟请求服务器获取分页数据，请替换成自己的网络请求
    const params = {
      pageNo,
      pageSize
    }
    const query = { current: params.pageNo, size: params.pageSize }
    if (orderBy.value !== 'all') {
      query.orderBy = orderBy.value
    } else {
      delete query.orderBy
    }
    if (taskType.value !== 'all') {
      query.type = taskType.value
    } else {
      delete query.type
    }
    if (minPrice.value) {
      query.startMoney = minPrice.value
    } else {
      delete query.startMoney
    }
    if (maxPrice.value) {
      query.endMoney = maxPrice.value
    } else {
      delete query.endMoney
    }
    generalAPI
      .getList(query)
      .then((res) => {
        paging.value.complete(res?.records || [])
      })
      .catch(() => {
        paging.value.complete(false)
      })
  }
  watch(orderBy, (val) => {
    paging.value.reload(true)
  })
  onShow(() => {
    getTaskTypeList()
    // 页面加载时自动触发查询
    if (dataList.value.length === 0) {
      nextTick(() => {
        paging.value.reload(true)
      })
    }
  })

  /* 获取任务类型字典数据 */
  const taskTypeList = ref([])
  const getTaskTypeList = async () => {
    try {
      const data = await generalAPI.getDictList('task_type')
      taskTypeList.value = data || []
    } catch (error) {
      console.error(error)
    }
  }

  /* 重置 */
  const handleReset = () => {
    taskType.value = 'all'
    minPrice.value = null
    maxPrice.value = null
  }

  const dropMenu = ref(null)
  /* 筛选确认 */
  const handleConfirm = () => {
    if (minPrice.value && maxPrice.value && minPrice.value > maxPrice.value) {
      uni.showToast({
        title: '最低金额不能大于最高金额',
        icon: 'none'
      })
      return
    }
    paging.value.reload(true)
    dropMenu.value.close()
  }

  function itemClick(item) {
    // 点击item时的处理逻辑
    uni.navigateTo({
      url: `/pages/index/taskDetail/index?id=${item.id}`
    })
  }
</script>

<style lang="scss" scoped>
  .price-range-container {
    display: flex;
    align-items: center;
    gap: 8rpx;
  }
  .split-text {
    padding: 0 10rpx;
    font-size: 28rpx;
    color: #999;
  }
  .content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 0 15px;
    box-sizing: border-box;

    .wd-card-item {
      width: 100%;
      :deep().wd-card {
        margin-left: 0;
        margin-right: 0;
      }
      .title {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
    }

    .earned {
      margin-right: 2px;
      color: #ec221f;
    }

    .remaining {
      margin-left: 5px;
      color: #999;
    }

    .num {
      font-size: 20px;
      font-weight: bold;
      color: #ec221f;
    }
  }
</style>
