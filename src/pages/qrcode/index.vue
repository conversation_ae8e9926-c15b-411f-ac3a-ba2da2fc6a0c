<!--
 * @Author: a<PERSON><PERSON><PERSON> abeleth<PERSON>@vip.qq.com
 * @Date: 2025-06-24 13:10:11
 * @LastEditors: abelethan <EMAIL>
 * @LastEditTime: 2025-07-15 20:21:42
 * @FilePath: pages/qrcode/index.vue
 * @Description: 二维码页面
 -->
<template>
  <view class="page-container">
    <!-- 装饰气泡 -->
    <view class="bubble bubble-1"></view>
    <view class="bubble bubble-2"></view>

    <!-- 主卡片内容 -->
    <view class="main-card">
      <view class="content-box">
        <!-- 顶部提示文字 -->
        <view class="instruction-text">
          <view class="decor-star">★</view>
          {{ wxInfo.title }}
          <view class="decor-star">★</view>
        </view>

        <!-- 居中的二维码容器 -->
        <view class="qrcode-container">
          <view class="qrcode-frame">
            <image
              v-if="img"
              class="qrcode-img"
              :src="img"
              mode="widthFix"
              show-menu-by-longpress="true"
            />
            <view v-else class="loading-state">
              <image src="/static/loading.png" class="loading-icon" />
              <text>加载中...</text>
            </view>
          </view>
        </view>

        <!-- 特殊提示（仅在微信公众号授权失败时显示） -->
        <view v-if="showAuthButton" class="auth-tip-box">
          <view class="auth-tip">需要获取您的微信身份信息以继续操作</view>
          <button class="auth-button" @click="triggerWechatAuth">授权获取身份</button>
        </view>

        <!-- 信息按钮 -->
        <view class="action-button">
          <text>{{ wxInfo.prompt }}</text>
        </view>

        <!-- 底部提示 -->
        <view class="hint-text">{{ wxInfo.describe }}</view>
      </view>
    </view>
  </view>
</template>

<script>
  import { getBaseConfigWxInfo, getRandomQrcode, getWechatOpenid } from '@/api/modules/qrcode'

  export default {
    data() {
      return {
        img: '',
        openid: '',
        cachedQRCode: null, // 二维码缓存
        wxInfo: {},
        textInfo: {},
        platform: '',
        isWechatBrowser: false, // 是否在微信内置浏览器
        authError: false, // 授权失败状态
        authErrorMessage: '', // 授权失败消息
        showAuthButton: false, // 是否显示授权按钮
        isInIframe: false // 是否在iframe中运行
      }
    },
    onLoad(options) {
      //  获取 URL 参数（uni-app 会自动解析 URL 参数到 options）
      this.code = options.code || ''

      // 环境检测
      this.checkEnvironment()

      // 获取页面配置信息
      this.getWxInfo()

      // 处理可能的授权重定向
      this.handleAuthorizationFlow()
    },
    methods: {
      // 环境检测
      checkEnvironment() {
        const appBaseInfo = uni.getAppBaseInfo()

        console.log(appBaseInfo)

        // 检测运行平台
        this.platform = appBaseInfo.uniPlatform

        // 检测是否是微信内置浏览器（H5环境）
        if (this.platform === 'h5') {
          try {
            const ua = window.navigator.userAgent.toLowerCase()
            console.log(ua)
            this.isWechatBrowser = ua.includes('micromessenger')

            // 检测是否在 iframe 中运行
            this.isInIframe = window.self !== window.top
          } catch (e) {
            this.isWechatBrowser = false
            this.isInIframe = false
          }
        } else {
          this.isWechatBrowser = false
          this.isInIframe = false
        }
      },

      // 统一错误处理
      handleError(message, err) {
        console.error('[授权错误]', message, err)
        this.authError = true
        this.authErrorMessage = message

        uni.showToast({
          title: message,
          icon: 'none',
          duration: 3000
        })
      },
      async getWxInfo() {
        try {
          const resp = await getBaseConfigWxInfo()
          this.wxInfo = resp
        } catch (err) {
          this.handleError('获取配置失败', err)
        }
      },

      // 处理授权流程
      async handleAuthorizationFlow() {
        console.log(this.platform)
        // 1. 检查是否有缓存的openid
        const cachedOpenid = await uni.getStorageSync('cachedOpenid')
        if (cachedOpenid) {
          this.openid = cachedOpenid
        } else {
          // 2. 根据运行环境选择授权方式
          // 2.1 在微信小程序环境
          if (this.platform === 'mp-weixin') {
            await this.getWeappOpenid()
          }
          // 2.2 在微信公众号环境
          else if (this.isWechatBrowser) {
            await this.getWechatPublicOpenid()
          }
        }

        await this.getImg()
      },

      // 获取小程序 OpenID
      async getWeappOpenid() {
        try {
          // 1.获取登录凭证
          const loginRes = await uni.login({ provider: 'weixin' })

          // 2.获取openid
          const sessionRes = await getWechatOpenid({ code: loginRes.code })

          // 3. 更新openid并缓存
          if (sessionRes.openid) {
            this.openid = sessionRes.openid
            uni.setStorageSync('cachedOpenid', this.openid)
            this.getImg()
          } else {
            this.handleError('小程序登录失败')
          }
        } catch (err) {
          this.handleError('小程序授权失败', err)
        }
      },
      // 获取公众号OpenID
      async getWechatPublicOpenid() {
        // 如果URL中有code参数（从微信授权返回）
        if (this.code) {
          try {
            // 通过code获取openid
            const response = await getWechatOpenid({
              code: this.code,
              source: 'public'
            })

            if (response.openid) {
              this.openid = response.openid
              // 缓存openid
              uni.setStorageSync('cachedOpenid', this.openid)
              // 清理URL中的code参数
              this.cleanUrl()
              // 加载图片
              this.getImg()
            } else {
              this.handleError('公众号身份获取失败')
            }
          } catch (err) {
            this.handleError('授权验证失败', err)
          }
        }
        // 无code参数，发起静默授权
        else {
          // 不在iframe中才能重定向
          if (!this.isInIframe) {
            this.initiateSilentAuthorization()
          }
          // 在iframe中则提示用户
          else {
            this.showAuthButton = true
          }
        }
      },

      // 清理 URL 中的授权参数（H5环境）
      cleanUrl() {
        // 只在小程序平台为 h5 时执行
        if (this.platform === 'h5') {
          try {
            // 移除 URL 中的 code 和 state 参数
            const url = window.location.href
              .replace(/[&?]code=[^&]+/, '')
              .replace(/[&?]state=[^&]+/, '')

            // 更新浏览器 URL（不触发刷新）
            window.history.replaceState(null, null, url)
          } catch (e) {
            console.warn('清理URL失败', e)
          }
        }
      },

      // 发起静默授权（微信公众号环境）
      initiateSilentAuthorization() {
        // 公众号AppID（需从后端获取或配置）
        const appId = this.wxInfo.appId || 'wx54a966b27bd41d86'

        // 当前页面URL作为回调地址（需编码）
        const redirectUri = encodeURIComponent(window.location.href)

        // 构建授权URL（使用snsapi_base模式）
        const authUrl = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${appId}&redirect_uri=${redirectUri}&response_type=code&scope=snsapi_base&state=wechat_public_auth&connect_redirect=1#wechat_redirect`

        // 重定向到微信授权页面
        location.href = authUrl
      },

      // 手动触发授权（特殊场景）
      triggerWechatAuth() {
        this.initiateSilentAuthorization()
      },

      // 获取二维码图片
      async getImg() {
        try {
          // 检查二维码缓存
          if (this.cachedQRCode) {
            this.img = this.cachedQRCode
          } else {
            // 调用接口获取二维码
            const resp = await getRandomQrcode({
              unionId: this.unionId,
              openId: this.openid,
              userId: ''
            })

            // 成功获取二维码
            if (resp) {
              this.img = resp
              this.cachedQRCode = this.img // 缓存二维码
              this.authError = false // 清除错误状态
            } else {
              this.handleError('二维码获取失败!')
              setTimeout(() => this.getImg(), 2000)
            }
          }
        } catch (err) {
          this.handleError('二维码加载失败', err)
        }
      }
    }
  }
</script>

<style>
  page {
    height: 100%;
    background: #c2c7fb;
  }

  view,
  image,
  text {
    box-sizing: border-box;
  }

  .page-container {
    width: 100%;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    overflow: hidden;
    padding: 100rpx 20rpx;
    background: linear-gradient(to bottom, #7681f8, #c2c7fb);
    font-family:
      -apple-system,
      BlinkMacSystemFont,
      Segoe UI,
      Roboto,
      PingFang SC,
      Microsoft YaHei,
      sans-serif;
  }

  .page-container:before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%237681f8' fill-opacity='0.05' fill-rule='evenodd'%3E%3Ccircle cx='6' cy='6' r='6'/%3E%3Ccircle cx='26' cy='26' r='6'/%3E%3C/g%3E%3C/svg%3E");
    z-index: -1;
  }

  .main-card {
    width: 100%;
    max-width: 700rpx;
    border-radius: 30rpx;
    overflow: hidden;
    margin: 40rpx 0 100rpx;
    z-index: 5;
    position: relative;
    background: linear-gradient(to bottom, #7681f8, #c2c7fb);
    box-shadow:
      5rpx 15rpx 50rpx rgba(0, 0, 0, 0.18),
      2rpx 8rpx 20rpx rgba(0, 0, 0, 0.5);
  }

  .main-card:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border: 5px solid rgba(255, 255, 255, 0.35);
    border-radius: 30rpx;
    pointer-events: none;
    z-index: 6;
  }

  .content-box {
    position: relative;
    z-index: 3;
    width: 100%;
    padding: 40rpx 30rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    background: rgba(255, 255, 255, 0.88);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    border-radius: 25rpx;
    border: 1rpx solid rgba(255, 255, 255, 0.5);
  }

  .content-box:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border: 2px solid rgba(255, 255, 255, 0.35);
    border-radius: 30rpx;
    pointer-events: none;
    z-index: 6;
  }

  .instruction-text {
    font-size: 36rpx;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 40rpx;
    text-align: center;
    display: flex;
    align-items: center;
    width: 100%;
    justify-content: center;
  }

  .decor-star {
    color: #3a7bff;
    font-size: 32rpx;
    margin: 0 15rpx;
  }

  .qrcode-container {
    background-color: #fff;
    border-radius: 15rpx;
    padding: 30rpx;
    display: inline-flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    box-shadow:
      5rpx 15rpx 50rpx rgba(0, 0, 0, 0.18),
      2rpx 8rpx 20rpx rgba(0, 0, 0, 0.5);
    margin-bottom: 30rpx;
    border: 1rpx solid #f0f7ff;
    width: 75%;
    max-width: 560rpx;
  }

  .qrcode-container:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border: 2px solid rgba(255, 255, 255, 0.35);
    border-radius: 30rpx;
    pointer-events: none;
    z-index: 6;
  }

  .qrcode-frame {
    background-color: #fff;
    border: 1rpx solid #e8f3ff;
    border-radius: 10rpx;
    width: 340rpx;
    height: 340rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    position: relative;
  }

  .qrcode-img {
    width: 320rpx;
    height: 320rpx;
    border-radius: 6rpx;
  }

  .loading-state {
    width: 320rpx;
    height: 320rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #3a7bff;
    font-size: 30rpx;
  }

  .loading-icon {
    width: 60rpx;
    height: 60rpx;
    margin-bottom: 20rpx;
    animation: spin 2s linear infinite;
  }

  .action-button {
    background: linear-gradient(to right, #3a7bff, #5d9bff);
    color: #fff;
    font-size: 28rpx;
    font-weight: 600;
    padding: 25rpx 50rpx;
    border-radius: 50rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 10rpx 30rpx rgba(58, 123, 255, 0.3);
    margin-bottom: 30rpx;
    width: 90%;
    max-width: 520rpx;
    position: relative;
    border: none;
  }

  .hint-text {
    font-size: 26rpx;
    color: #7f8c8d;
    text-align: center;
    margin-top: 15rpx;
    padding: 0 30rpx;
    line-height: 1.6;
  }

  @keyframes spin {
    0% {
      transform: rotate(0);
    }

    to {
      transform: rotate(360deg);
    }
  }

  .bubble {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.15);
    z-index: -1;
  }

  .bubble-1 {
    width: 300rpx;
    height: 300rpx;
    top: 10%;
    right: -50rpx;
  }

  .bubble-2 {
    width: 200rpx;
    height: 200rpx;
    bottom: 15%;
    left: -30rpx;
  }

  .auth-tip-box {
    text-align: center;
    margin: 15px 0;
    padding: 10px;
    border-radius: 8px;
    background-color: #fff8e1;
  }

  .auth-tip {
    font-size: 14px;
    color: #ff9800;
    margin-bottom: 12px;
  }

  .auth-button {
    background-color: #07c160;
    color: white;
    border-radius: 20px;
    font-size: 14px;
    padding: 6px 16px;
  }

  /* 自适应样式调整 */
  @media screen and (max-width: 480px) {
    .auth-tip-box {
      margin: 10px 0;
    }

    .auth-button {
      font-size: 13px;
      padding: 5px 12px;
    }
  }

  @media (max-width: 480px) {
    .main-card {
      max-width: 85%;
      margin-top: 15%;
    }
  }
</style>
