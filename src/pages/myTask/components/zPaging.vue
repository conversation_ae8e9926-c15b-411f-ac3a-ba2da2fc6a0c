<!-- 自定义下拉刷新与上拉加载演示(vue) -->
<!-- https://z-paging.zxlee.cn/start/demo.html -->
<template>
  <view class="z-paging-content zpageing-content">
    <z-paging ref="paging" v-model="dataList" @query="queryList" :auto="false">
      <!-- 需要固定在顶部不滚动的view放在slot="top"的view中，如果需要跟着滚动，则不要设置slot="top" -->
      <!-- 注意！此处的z-tabs为独立的组件，可替换为第三方的tabs，若需要使用z-tabs，请在插件市场搜索z-tabs并引入，否则会报插件找不到的错误 -->
      <template #top>
        <!-- <z-tabs :list="tabList" @change="tabsChange" /> -->
        <!-- tabs占位 -->
        <!-- <view class="h-42px pointer-events-none"></view> -->
        <wd-tabs v-model="tabIndex" @change="tabsChange">
          <wd-tab title="进行中"> </wd-tab>
          <wd-tab title="待审核"> </wd-tab>
          <wd-tab title="已通过"> </wd-tab>
          <wd-tab title="未通过"> </wd-tab>
        </wd-tabs>
      </template>

      <!-- 自定义下拉刷新view(如果use-custom-refresher为true且不设置下面的slot="refresher"，此时不用获取refresherStatus，会自动使用z-paging自带的下拉刷新view) -->

      <!-- 注意注意注意！！字节跳动小程序中自定义下拉刷新不支持slot-scope，将导致custom-refresher无法显示 -->
      <!-- 如果是字节跳动小程序，请参照sticky-demo.vue中的写法，此处使用slot-scope是为了减少data中无关变量声明，降低依赖 -->
      <template #refresher="{ refresherStatus }">
        <!-- 此处的custom-refresh为demo中自定义的组件，非z-paging的内置组件，请在实际项目中自行创建。这里插入什么view，下拉刷新就显示什么view -->
        <custom-refresher :status="refresherStatus" />
      </template>
      <!-- 自定义没有更多数据view -->
      <template #loadingMoreNoMore>
        <!-- 此处的custom-nomore为demo中自定义的组件，非z-paging的内置组件，请在实际项目中自行创建。这里插入什么view，没有更多数据就显示什么view -->
        <custom-nomore />
      </template>

      <!-- 如果希望其他view跟着页面滚动，可以放在z-paging标签内 -->
      <view class="content">
        <view
          v-for="item in dataList"
          class="wd-card-item"
          :key="item.id"
          @click.stop="itemClick(item)"
        >
          <wd-card>
            <template #title>
              <view class="title">
                <view>{{ item.title }}</view>
                <view class="title-tip"> <text>+</text>{{ item.money }}元 </view>
              </view>
            </template>
            <view class="text-gray-500 text-xs content-main">
              <view class="time text-ellipsis">报名时间：{{ item.createTime }}</view>
              <view
                v-if="item.auditStatus === 0 || item.auditStatus === 3"
                class="task-status text-gray-500 text-xs"
                >{{
                  item.status === 0 ? `任务进行中（请在${item.expireTime}前提交）` : '任务已超时'
                }}</view
              >
              <text v-if="item.auditStatus === 3" class="text-gray-500 text-xs"
                >反馈意见：{{ item.feedback }}</text
              >
            </view>
            <template v-if="item.auditStatus === 0 || item.auditStatus === 3" #footer>
              <view class="flex justify-end items-center">
                <view
                  @click.stop="handleClickDel('del', item)"
                  class="mr-1"
                  v-if="item.status === 1"
                  ><wd-button size="small" type="error" plain>删除记录</wd-button></view
                >
                <view v-if="item.status === 1" class="mr-1"
                  ><wd-button @click.stop="itemClick(item)" size="small" type="error"
                    >重新报名</wd-button
                  ></view
                >
                <view
                  @click.stop="handleClickDel('cancel', item)"
                  v-if="item.status === 0"
                  class="mr-1"
                  ><wd-button size="small" type="error">取消报名</wd-button></view
                >
              </view>
            </template>
          </wd-card>
        </view>
      </view>
    </z-paging>
  </view>
  <wd-toast selector="wd-toast-box-slot"> </wd-toast>
</template>

<script setup>
  import { generalAPI } from '@/api/modules/general'
  import { del } from 'vue-demi'
  import { useToast } from 'wot-design-uni'

  const toast = useToast('wd-toast-box-slot')
  const dataList = ref([])
  const tabIndex = ref(0)
  const paging = ref(null)

  function tabsChange(index) {
    tabIndex.value = index
    // 当切换tab或搜索时请调用组件的reload方法，请勿直接调用：queryList方法！！
    // 调用reload时参数传true则代表reload时触发下拉刷新效果，不传或false则代表取消此效果
    paging.value.reload(true)
  }

  const queryList = (pageNo, pageSize) => {
    const params = {
      pageNo,
      pageSize
    }
    generalAPI
      .getMyTaskList({ current: params.pageNo, size: params.pageSize, auditStatus: tabIndex.value })
      .then((res) => {
        paging.value.complete(res || [])
      })
      .catch(() => {
        paging.value.complete(false)
      })
  }

  onShow(() => {
    // 页面加载时自动触发查询
    console.log('dataList.value ~ 🚀🚀🚀🚀 ~: zPaging ~ 行:125', dataList.value)
    if (dataList.value.length === 0) {
      queryList(1, 10)
    } else {
      paging.value.reload(true)
    }
  })

  const itemClick = (item) => {
    // 点击item时的处理逻辑
    uni.navigateTo({
      url: `/pages/index/taskDetail/index?id=${item.taskId}`
    })
  }

  const handleClickDel = async (type, item) => {
    try {
      const res = await generalAPI.deleteMyTask(item.id)
      toast.success(type === 'cancel' ? '取消报名成功' : '删除记录成功')
    } catch (error) {
      console.error('Error deleting task:', error)
      // 处理错误情况
      toast.error(type === 'cancel' ? '取消报名失败，请稍后再试' : '删除失败，请稍后再试')
      return
    } finally {
      paging.value.reload(true) // 重新加载数据
    }
  }
</script>
<style lang="scss" scoped>
  .content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    .content-main {
      .time {
        color: #696969;
      }
      .task-status {
        color: #e24139;
      }
    }

    .wd-card-item {
      width: 100%;
      margin-top: 5px;
      .title {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
      }
      .title-tip {
        color: #e83836;
        font-size: 16px;
        display: flex;
        align-items: center;
        font-weight: bold;
      }
    }
  }
</style>
