<script setup>
  import { generalAPI } from '../../api/modules/general'
  import BindWechatDialog from '../balance/components/BindWechatDialog.vue'
  const router = useRouter()

  const userStore = useOutsideUserStore()
  const userInfo = computed(() => userStore.userInfo)

  const genderText = computed(() => {
    const genderMap = {
      0: '未设置',
      1: '男',
      2: '女'
    }
    return genderMap[userInfo.value.gender] || '未设置'
  })

  function formatPhone(phone) {
    if (!phone) return '未设置'
    return phone.replace(/(\d{3})(\d{4})(\d{4})/, '$1****$3')
  }

  function changeAvatar(e) {
    console.log('e ~ 🚀🚀🚀🚀 ~: index ~ 行:22', e)
    const avatar = e.detail.avatarUrl
    uni.uploadFile({
      url: `${import.meta.env.VITE_APP_API_BASEURL}/common/file/oss/upload`,
      filePath: avatar,
      name: 'file',
      success: (res) => {
        const result = JSON.parse(res.data)
        userStore.setUserInfoAvatar(result.data.fileUrl)
      },
      fail: (err) => {
        console.error('上传失败:', err)
      }
    })
  }
  function userNameInput(e) {
    const name = e.detail.value
    userStore.setUserInfoNickName(name)
  }

  async function saveProfile() {
    try {
      await generalAPI.updateUserInfo({
        name: userInfo.value.nickName,
        avatar: userInfo.value.avatar,
        nickName: userInfo.value.nickName,
        tel: userInfo.value.tel
      })
      showToast('保存成功')
    } catch (error) {
      showToast('保存失败，请重试', 'none')
    }
  }

  function showToast(title, icon = 'success') {
    uni.showToast({
      title,
      icon
    })
  }
  /* 获取手机号 */
  const onGetPhoneNumber = (e) => {
    if (e.detail.errMsg === 'getPhoneNumber:ok') {
      // 👇 调用你后端的解密接口
      generalAPI
        .getPhone({ code: e.detail.code })
        .then((res) => {
          userStore.setUserInfoTel(res.phoneNumber)
        })
        .catch((error) => {
          console.error(error)
        })
    } else {
      console.warn('用户拒绝授权')
    }
  }

  function handleLogout() {
    uni.showModal({
      title: '提示',
      content: '确定要退出登录吗?',
      showCancel: true,
      confirmText: '确定',
      cancelText: '取消',
      success: async function (res) {
        if (res.confirm) {
          await userStore.logout()
          router.replace({
            path: '/login'
          })
        } else if (res.cancel) {
          console.log('用户点击取消')
        }
      }
    })
  }

  /* 是否已经绑定微信 */
  const isBindWechat = computed(() => {
    const wxinfo = JSON.parse(userStore.userInfo?.wxinfo || '{}')
    return !!wxinfo.weixin?.openid
  })

  const showDialog = ref(false)
  const handleClickBindWeChat = () => {
    if (isBindWechat.value) return
    showDialog.value = true
  }
</script>

<template>
  <view class="profile-edit-page h-full flex flex-col bg-gray-50">
    <view class="header-section relative">
      <view class="relative h-40 overflow-hidden bg-primary-400">
        <view class="absolute h-32 w-32 rounded-full bg-white opacity-10 -right-8 -top-8"></view>
        <view class="absolute bottom-4 right-16 h-16 w-16 rounded-full bg-white opacity-10"></view>
      </view>

      <view class="absolute bottom-0 left-1/2 translate-y-1/2 transform -translate-x-1/2">
        <view class="relative">
          <view class="h-24 w-24 overflow-hidden border-4 border-white rounded-full shadow-lg">
            <button
              class="h-24 w-24 overflow-hidden border-4 border-white rounded-full shadow-lg px-0"
              open-type="chooseAvatar"
              @chooseavatar="changeAvatar"
            >
              <image
                v-if="userInfo.avatar"
                :src="userInfo.avatar"
                class="size-full"
                mode="aspectFill"
              />

              <image v-else src="/static/avatar.jpeg" class="size-full" mode="aspectFill" />
            </button>
          </view>
          <view
            class="absolute bottom-0 right-0 h-7 w-7 flex items-center justify-center border-2 border-white rounded-full bg-primary-500"
          >
            <view class="i-carbon-edit text-sm text-white"></view>
          </view>
        </view>
      </view>
    </view>

    <view class="content-section flex-1 px-4 pb-4 pt-16">
      <view class="info-card mb-4 overflow-hidden rounded-xl bg-white shadow-sm">
        <view class="card-header">
          <view class="i-carbon-user text-lg text-primary-500"></view>
          <view class="card-title"> 基本信息 </view>
        </view>

        <view class="divide-y divide-gray-100">
          <view class="info-item">
            <view class="info-label">
              <view class="i-carbon-user-identification text-lg text-gray-400"></view>
              <text>用户名</text>
            </view>
            <view class="info-value">
              <input
                type="nickname"
                style="width: 300rpx"
                :value="userInfo.nickName"
                @blur="userNameInput"
              />
              <view class="i-carbon-chevron-right text-sm text-gray-400"></view>
            </view>
          </view>
        </view>
        <view class="divide-y divide-gray-100">
          <view class="info-item">
            <view class="info-label">
              <view class="i-carbon-user-identification text-lg text-gray-400"></view>
              <text>手机号</text>
            </view>

            <view class="info-value">
              <text style="width: 300rpx; margin-right: 0">{{ userInfo.tel }}</text>
              <!-- <button
                class="get-phone-btn"
                open-type="getPhoneNumber"
                @getphonenumber="onGetPhoneNumber"
                style="
                  position: absolute;
                  top: 0;
                  left: 0;
                  right: 0;
                  bottom: 0;
                  background: none;
                  border: none;
                  margin: 0;
                  padding: 0;
                  opacity: 0;
                "
              >
              </button> -->
              <view class="i-carbon-chevron-right text-sm text-gray-400"></view>
            </view>
          </view>
        </view>
        <view @click="handleClickBindWeChat" class="divide-y divide-gray-100">
          <view class="info-item">
            <view class="info-label">
              <view class="i-carbon-user-identification text-lg text-gray-400"></view>
              <text>微信</text>
            </view>
            <view class="info-value">
              <text>{{ isBindWechat ? '已绑定' : '未绑定' }}</text>
              <view class="i-carbon-chevron-right text-sm text-gray-400"></view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <view class="action-section px-4 pb-8 pt-4">
      <wd-button
        type="primary"
        custom-class="w-full flex items-center justify-center rounded-lg bg-primary-500 py-3 text-white font-medium transition-colors duration-200 active:bg-primary-600"
        hover-class="bg-primary-600"
        @click="saveProfile"
      >
        <view class="i-carbon-save mr-2 text-lg"></view>
        保存修改
      </wd-button>
      <wd-button
        type="error"
        custom-class="w-full flex items-center justify-center rounded-lg bg-primary-500 py-3 text-white font-medium transition-colors duration-200 active:bg-primary-600"
        hover-class="bg-primary-600"
        @click="handleLogout"
      >
        退出登录
      </wd-button>
    </view>
    <BindWechatDialog v-model:visible="showDialog"></BindWechatDialog>
  </view>
</template>

<style scoped>
  /* 信息卡片样式 */
  .info-card {
    @apply bg-white rounded-xl shadow-sm overflow-hidden;
  }

  .card-header {
    @apply flex items-center px-5 py-4 border-b border-gray-100;
  }

  .card-title {
    @apply ml-3 text-lg font-bold text-gray-800;
  }

  .info-item {
    @apply flex items-center justify-between px-5 py-4 active:bg-gray-50 transition-colors duration-200;
  }

  .info-label {
    @apply flex items-center text-gray-700 font-medium;
  }

  .info-label text {
    @apply ml-3;
  }

  .info-value {
    @apply flex items-center text-gray-600;
  }
  .info-value text {
    @apply mr-2;
  }

  @media (max-width: 375px) {
    .info-item {
      @apply px-4 py-3;
    }

    .card-header {
      @apply px-4 py-3;
    }
  }
</style>
