<template>
  <view>
    <view class="p-4">
      <!-- 富文本解析 -->
      <uv-parse :content="tipStr"></uv-parse>
    </view>
  </view>
</template>

<script setup>
  import { generalAPI } from '@/api/modules/general'
  const props = defineProps({
    argumentType: {
      type: String
    }
  })
  const argumentTypeRef = toRef(props, 'argumentType')
  const tipList = ref([])
  const getTip = async () => {
    try {
      const data = await generalAPI.getTypeInfo(argumentTypeRef.value === 'product' ? 5 : 2)
      tipList.value = data || []
    } catch (error) {
      console.error(error)
    }
  }
  const tipStr = computed(() => {
    return tipList.value.map((item) => item.content).join('；')
  })
  watch(
    argumentTypeRef,
    (val) => {
      console.log('val ~ 🚀🚀🚀🚀 ~: agreement ~ 行:32', val)
      getTip()
    },
    {
      immediate: true
    }
  )
</script>
