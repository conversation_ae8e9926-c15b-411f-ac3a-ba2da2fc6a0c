<template>
  <view class="relative h-screen overflow-hidden from-blue-50 to-blue-100/70 bg-gradient-to-br">
    <view class="absolute left-0 top-0 h-full w-full opacity-5">
      <view class="absolute right-10 top-20 h-32 w-32 rounded-full bg-blue-400 blur-3xl"></view>
      <view class="absolute bottom-40 left-10 h-24 w-24 rounded-full bg-blue-400 blur-2xl"></view>
    </view>

    <view class="relative z-10 min-h-screen flex flex-col">
      <view class="flex flex-1 flex-col justify-center px-6 py-12">
        <view class="mb-12 text-center">
          <view class="relative mb-6 inline-block">
            <image src="/static/logo.png" alt="App Logo" class="h-24 w-24 rounded-2xl" />

            <view
              class="absolute rounded-3xl from-blue-400 to-blue-400 bg-gradient-to-r opacity-20 blur-lg -inset-2"
            >
            </view>
          </view>

          <view class="space-y-2">
            <text class="block text-3xl text-gray-900 font-bold">
              {{ appName }}
            </text>
            <!-- <text class="block text-base text-gray-600">
              {{ description }}
            </text> -->
          </view>
        </view>

        <view @tap="handlePhoneClick" class="space-y-6">
          <!-- <button
            v-if="agreed"
            class="relative w-full overflow-hidden rounded-2xl from-blue-500 to-blue-400 bg-gradient-to-r px-6 font-semibold shadow-lg transition-all duration-200 active:scale-98 disabled:cursor-not-allowed !text-white disabled:opacity-70"
            :class="{ 'shadow-xl': !isLoading }"
            :disabled="isLoading"
            open-type="getPhoneNumber"
            @getphonenumber="onGetPhoneNumber"
          >
            <view class="flex items-center justify-center space-x-3">
              <view v-if="isLoading" class="i-carbon-fade h-5 w-5 animate-spin bg-white"></view>
              <view v-else class="i-carbon-phone h-5 w-5"></view>
              <text>{{ isLoading ? '登录中...' : '手机号一键登录' }}</text>
            </view>

            <view
              class="absolute inset-0 from-transparent via-white to-transparent bg-gradient-to-r opacity-0 transition-all duration-500 -translate-x-full group-active:translate-x-full group-active:opacity-20"
            >
            </view>
          </button> -->
          <button
            class="relative w-full overflow-hidden rounded-2xl from-blue-500 to-blue-400 bg-gradient-to-r px-6 font-semibold shadow-lg transition-all duration-200 active:scale-98 disabled:cursor-not-allowed !text-white disabled:opacity-70"
            :class="{ 'shadow-xl': !isLoading }"
            :disabled="isLoading"
            @click="handleCLickLogin"
          >
            <view class="flex items-center justify-center space-x-3">
              <view v-if="isLoading" class="i-carbon-fade h-5 w-5 animate-spin bg-white"></view>
              <!-- <view v-else class="i-carbon-phone h-5 w-5"></view> -->
              <text>{{ isLoading ? '登录中...' : '微信一键登录' }}</text>
            </view>

            <view
              class="absolute inset-0 from-transparent via-white to-transparent bg-gradient-to-r opacity-0 transition-all duration-500 -translate-x-full group-active:translate-x-full group-active:opacity-20"
            >
            </view>
          </button>
          <button
            type="info"
            @click="handleClickBack"
            class="mt-3! relative w-full overflow-hidden rounded-2xl from-gray-500 to-gray-400 bg-gradient-to-r px-6 font-semibold shadow-lg transition-all duration-200 active:scale-98 disabled:cursor-not-allowed !text-white disabled:opacity-70"
          >
            <view class="flex items-center justify-center space-x-3">
              <text>返回</text>
            </view>

            <view
              class="absolute inset-0 from-transparent via-white to-transparent bg-gradient-to-r opacity-0 transition-all duration-500 -translate-x-full group-active:translate-x-full group-active:opacity-20"
            >
            </view>
          </button>
          <view class="mt-2 flex items-center px-2 space-x-2">
            <view
              class="h-4 w-4 flex flex-shrink-0 items-center justify-center border-solid border-1 border-gray-500 rounded transition-all duration-200 active:scale-95"
              :class="agreed ? 'bg-blue-500 border-blue-500' : 'bg-white'"
              @click="toggleAgreement"
            >
              <view v-if="agreed" class="i-carbon-checkmark h-3 w-3 text-white"></view>
            </view>

            <view class="mt-[1px] flex-1 leading-relaxed">
              <text class="text-sm text-gray-600">
                请阅读协议
                <text
                  class="text-blue-600 font-medium transition-colors duration-200 active:text-blue-700"
                  @click.stop="onAgreementClick('product')"
                >
                  《用户协议》
                </text>
                <text
                  class="text-blue-600 font-medium transition-colors duration-200 active:text-blue-700"
                  @click.stop="onAgreementClick('privacy')"
                >
                  《隐私政策》
                </text>
              </text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
  <!-- demo 基础用法 -->
  <wd-message-box selector="wd-message-box-slot">
    <Agreement :argumentType="argumentType" />
  </wd-message-box>
  <wd-toast selector="wd-toast-box-slot"> </wd-toast>
</template>

<script setup>
  // import { description, version } from '@root/package.json'
  import { generalAPI } from '../../api/modules/general'
  import Agreement from './components/agreement.vue'
  import { useMessage, useToast } from 'wot-design-uni'
  const userStore = useOutsideUserStore()
  const router = useRouter()
  const message = useMessage('wd-message-box-slot')

  const agreed = ref(false)
  const isLoading = ref(false)
  const agreementVisible = ref(false)

  const appName = '星愿赏'
  const toast = useToast('wd-toast-box-slot')
  async function onLoginClick() {
    if (!agreed.value) {
      toast.info('请先同意服务协议')
      return
    }

    try {
      isLoading.value = true
      uni.login({
        provider: 'weixin',
        success: (res) => {
          const code = res?.code || res?.authResult?.unionid
          const openid = res?.authResult?.openid || 'null'
          console.log('res ~ 🚀🚀🚀🚀 ~: index ~ 行:111', res)
          uni.getUserInfo({
            provider: 'weixin',
            success: (userRes) => {
              console.log('userRes ~ 🚀🚀🚀🚀 ~: index ~ 行:116', userRes)
              userStore.login(code, openid, userRes.userInfo).then(() => {
                toast.success('登录成功')
                setTimeout(() => {
                  isLoading.value = false

                  router.pushTab({
                    path: '/pages/index/index'
                  })
                }, 500)
              })
            }
          })
        }
      })
    } catch (error) {
      isLoading.value = false
      toast.error('登录失败，请重试')
    }
  }
  const handleCLickLogin = () => {
    if (!agreed.value) {
      toast.info('请先同意服务协议')
    } else {
      onLoginClick()
    }
  }
  const onGetPhoneNumber = (e) => {
    isLoading.value = true

    if (e.detail.errMsg === 'getPhoneNumber:ok') {
      // 👇 调用你后端的解密接口
      generalAPI
        .getPhone({ code: e.detail.code })
        .then((res) => {
          // userStore.setUserInfoTel(res.phoneNumber)
          userStore.loginByPhone(res.phoneNumber).then(() => {
            toast.success('登录成功')
            userStore.getUserInfo()
            setTimeout(() => {
              isLoading.value = false

              router.pushTab({
                path: '/pages/index/index'
              })
            }, 500)
          })
        })
        .catch((error) => {
          isLoading.value = false

          console.error(error)
        })
    } else {
      isLoading.value = false
      console.warn('用户拒绝授权')
    }
  }
  function toggleAgreement() {
    agreed.value = !agreed.value
  }
  const argumentType = ref('')
  function onAgreementClick(type) {
    argumentType.value = type
    agreementVisible.value = true
    message.alert({
      title: type === 'product' ? '《产品服务协议》' : '《隐私政策》'
    })
    // uni.showModal({
    // title: '提示',
    //  content: '触发了路由中间件，是否允许通过?',
    //  success: (res) => {}
    // })
  }

  const handleClickBack = () => {
    /* 回到首页 */
    router.pushTab({
      path: '/pages/index/index'
    })
  }
</script>

<style scoped lang="scss">
  @keyframes float {
    0%,
    100% {
      transform: translateY(0px);
    }

    50% {
      transform: translateY(-10px);
    }
  }

  .animate-float {
    animation: float 3s ease-in-out infinite;
  }

  @keyframes spin {
    from {
      transform: rotate(0deg);
    }

    to {
      transform: rotate(360deg);
    }
  }

  .animate-spin {
    animation: spin 1s linear infinite;
  }
</style>
