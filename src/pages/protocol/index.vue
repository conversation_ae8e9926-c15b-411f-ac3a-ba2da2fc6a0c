<template>
  <view class="h-screen w-screen p-4 box-border">
    <view class="p-4">
      <!-- 富文本解析 -->
      <uv-parse :content="tipStr"></uv-parse>
    </view>
  </view>
</template>

<script setup>
  import { generalAPI } from '@/api/modules/general'

  const tipList = ref([])
  const getTip = async (argumentType) => {
    try {
      const data = await generalAPI.getTypeInfo(argumentType === 'product' ? 5 : 2)
      tipList.value = data || []
    } catch (error) {
      console.error(error)
    }
  }
  const tipStr = computed(() => {
    return tipList.value.map((item) => item.content).join('；')
  })

  onLoad((option) => {
    const { argumentType } = option
    getTip(argumentType)
    uni.setNavigationBarTitle({
      title: argumentType === 'product' ? '用户协议' : '隐私协议'
    })
  })
</script>
