<!-- 自定义下拉刷新与上拉加载演示(vue) -->
<!-- https://z-paging.zxlee.cn/start/demo.html -->
<template>
  <view class="z-paging-content zpageing-content">
    <z-paging
      ref="paging"
      v-model="dataList"
      @query="queryList"
      :auto="false"
      :loading-more-enabled="false"
    >
      <!-- 需要固定在顶部不滚动的view放在slot="top"的view中，如果需要跟着滚动，则不要设置slot="top" -->
      <!-- 注意！此处的z-tabs为独立的组件，可替换为第三方的tabs，若需要使用z-tabs，请在插件市场搜索z-tabs并引入，否则会报插件找不到的错误 -->
      <template #top>
        <!-- <z-tabs :list="tabList" @change="tabsChange" /> -->
        <!-- tabs占位 -->
        <!-- <view class="h-42px pointer-events-none"></view> -->
        <wd-tabs custom-class="custom-tabs" v-model="tabIndex1" @change="tabsChange1">
          <wd-tab v-for="(item, index) in tabsList" :title="item.title" :key="item.title + index">
            <wd-tabs
              v-if="item?.children && item?.children?.length > 0"
              v-model="tabIndex2"
              @change="tabsChange2"
            >
              <wd-tab
                v-for="(item2, index2) in item.children"
                :title="item2.title"
                :key="item2.title + index2"
              >
              </wd-tab>
            </wd-tabs>
          </wd-tab>
        </wd-tabs>
      </template>

      <!-- 自定义下拉刷新view(如果use-custom-refresher为true且不设置下面的slot="refresher"，此时不用获取refresherStatus，会自动使用z-paging自带的下拉刷新view) -->

      <!-- 注意注意注意！！字节跳动小程序中自定义下拉刷新不支持slot-scope，将导致custom-refresher无法显示 -->
      <!-- 如果是字节跳动小程序，请参照sticky-demo.vue中的写法，此处使用slot-scope是为了减少data中无关变量声明，降低依赖 -->
      <template #refresher="{ refresherStatus }">
        <!-- 此处的custom-refresh为demo中自定义的组件，非z-paging的内置组件，请在实际项目中自行创建。这里插入什么view，下拉刷新就显示什么view -->
        <custom-refresher :status="refresherStatus" />
      </template>
      <!-- 自定义没有更多数据view -->
      <template #loadingMoreNoMore>
        <!-- 此处的custom-nomore为demo中自定义的组件，非z-paging的内置组件，请在实际项目中自行创建。这里插入什么view，没有更多数据就显示什么view -->
        <custom-nomore />
      </template>

      <!-- 如果希望其他view跟着页面滚动，可以放在z-paging标签内 -->
      <view class="content">
        <view class="rank-item" v-for="(item, index) in dataList" :key="item.id || index">
          <view class="rank-index" :class="getRankClass(index)">
            <text class="rank-text" v-if="index > 2">{{ index + 1 }}</text>
            <image class="rank-icon" v-else :src="getRankIcon(index)" mode="scaleToFill" />
          </view>
          <image
            class="avatar"
            :src="item.avatar ? item.avatar : '/static/avatar.jpeg'"
            mode="aspectFill"
          />
          <view class="rank-content">
            <view class="rank-name">{{ item.name }}</view>
            <view class="rank-task">{{ item.subtitle }}：{{ item.num }} {{ item.unit }}</view>
          </view>
        </view>
      </view>
    </z-paging>
  </view>
  <wd-toast selector="wd-toast-box-slot"> </wd-toast>
</template>

<script setup>
  import { generalAPI } from '@/api/modules/general'
  import { useToast } from 'wot-design-uni'

  const toast = useToast('wd-toast-box-slot')
  const dataList = ref([])

  const tabsList = ref([])
  const tabIndex1 = ref(0)
  const tabIndex2 = ref(0)
  const paging = ref(null)

  const getRankClass = (index) => {
    if (index === 0) return 'rank-first'
    if (index === 1) return 'rank-second'
    if (index === 2) return 'rank-third'
    return ''
  }
  const getRankIcon = (index) => {
    return `../../../static/rank-${index + 1}.png`
  }

  function tabsChange1(index) {
    tabIndex1.value = index
    // 当切换tab或搜索时请调用组件的reload方法，请勿直接调用：queryList方法！！
    // 调用reload时参数传true则代表reload时触发下拉刷新效果，不传或false则代表取消此效果
    paging.value.reload(true)
  }

  function tabsChange2(index) {
    tabIndex2.value = index
    // 当切换tab或搜索时请调用组件的reload方法，请勿直接调用：queryList方法！！
    // 调用reload时参数传true则代表reload时触发下拉刷新效果，不传或false则代表取消此效果
    paging.value.reload(true)
  }

  const queryList = (pageNo, pageSize) => {
    const params = {
      pageNo,
      pageSize
    }
    generalAPI
      .getRankJson()
      .then((res) => {
        const data = JSON.parse(res)
        tabsList.value = data
        let list = []
        if (data[tabIndex1.value]?.children) {
          list = data[tabIndex1.value].children[tabIndex2.value].dataList
        } else {
          list = data[tabIndex1.value].dataList
        }
        paging.value.complete(list)
      })
      .catch(() => {
        paging.value.complete(false)
      })
  }

  onShow(() => {
    // 页面加载时自动触发查询
    queryList(1, 10)
  })
</script>
<style lang="scss" scoped>
  .content {
    display: flex;
    flex-direction: column;
    gap: 16rpx;
    padding: 24rpx;
    box-sizing: border-box;

    .rank-item {
      display: flex;
      align-items: center;
      padding: 20rpx;
      border-radius: 20rpx;
      background-color: #fff;
      box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.04);
    }
    .rank-index {
      width: 40rpx;
      font-weight: bold;
      font-size: 32rpx;
      text-align: center;
      margin-right: 20rpx;
      color: #999;
      .rank-icon {
        width: 40rpx;
        height: 40rpx;
      }
      .rank-text {
        width: 40rpx;
        height: 40rpx;
        background-color: #f5f5f5;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
    .avatar {
      width: 72rpx;
      height: 72rpx;
      border-radius: 50%;
      margin-right: 20rpx;
    }
    .rank-content {
      flex: 1;
      display: flex;
      justify-content: space-between;
    }
    .rank-name {
      font-size: 28rpx;
      font-weight: 500;
      color: #333;
    }
    .rank-task {
      font-size: 24rpx;
      color: #888;
      margin-top: 6rpx;
    }
    .rank-first {
      color: #ff3d00;
      font-size: 36rpx;
    }
    .rank-second {
      color: #2196f3;
      font-size: 34rpx;
    }
    .rank-third {
      color: #ff9800;
      font-size: 32rpx;
    }
  }
</style>
