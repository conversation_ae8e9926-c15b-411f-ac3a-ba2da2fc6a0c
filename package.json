{"name": "uni-preset-vue", "type": "commonjs", "version": "1.0.0", "scripts": {"dev:app": "uni -p app", "dev:app-android": "uni -p app-android", "dev:app-ios": "uni -p app-ios", "dev:custom": "uni -p", "dev:h5": "uni", "dev:h5:ssr": "uni --ssr", "dev:mp-alipay": "uni -p mp-alipay", "dev:mp-baidu": "uni -p mp-baidu", "dev:mp-jd": "uni -p mp-jd", "dev:mp-kuaishou": "uni -p mp-kua<PERSON>ou", "dev:mp-lark": "uni -p mp-lark", "dev:mp-qq": "uni -p mp-qq", "dev:mp-toutiao": "uni -p mp-to<PERSON><PERSON>", "dev:mp-weixin": "uni -p mp-weixin", "dev:mp-xhs": "uni -p mp-xhs", "dev:quickapp-webview": "uni -p quickapp-webview", "dev:quickapp-webview-huawei": "uni -p quickapp-webview-huawei", "dev:quickapp-webview-union": "uni -p quickapp-webview-union", "build:app": "uni build -p app", "build:app-android": "uni build -p app-android", "build:app-ios": "uni build -p app-ios", "build:custom": "uni build -p", "build:h5": "uni build", "build:h5:ssr": "uni build --ssr", "build:mp-alipay": "uni build -p mp-alipay", "build:mp-baidu": "uni build -p mp-baidu", "build:mp-jd": "uni build -p mp-jd", "build:mp-kuaishou": "uni build -p mp-kuaishou", "build:mp-lark": "uni build -p mp-lark", "build:mp-qq": "uni build -p mp-qq", "build:mp-toutiao": "uni build -p mp-to<PERSON>ao", "build:mp-weixin": "uni build -p mp-weixin", "build:mp-xhs": "uni build -p mp-xhs", "build:quickapp-webview": "uni build -p quickapp-webview", "build:quickapp-webview-huawei": "uni build -p quickapp-webview-huawei", "build:quickapp-webview-union": "uni build -p quickapp-webview-union", "prepare": "husky", "cz": "git-cz", "preinstall": "npx only-allow pnpm"}, "dependencies": {"@dcloudio/uni-app": "3.0.0-4060420250429001", "@dcloudio/uni-app-harmony": "3.0.0-4060420250429001", "@dcloudio/uni-app-plus": "3.0.0-4060420250429001", "@dcloudio/uni-components": "3.0.0-4060420250429001", "@dcloudio/uni-h5": "3.0.0-4060420250429001", "@dcloudio/uni-mp-alipay": "3.0.0-4060420250429001", "@dcloudio/uni-mp-baidu": "3.0.0-4060420250429001", "@dcloudio/uni-mp-harmony": "3.0.0-4060420250429001", "@dcloudio/uni-mp-jd": "3.0.0-4060420250429001", "@dcloudio/uni-mp-kuaishou": "3.0.0-4060420250429001", "@dcloudio/uni-mp-lark": "3.0.0-4060420250429001", "@dcloudio/uni-mp-qq": "3.0.0-4060420250429001", "@dcloudio/uni-mp-toutiao": "3.0.0-4060420250429001", "@dcloudio/uni-mp-weixin": "3.0.0-4060420250429001", "@dcloudio/uni-mp-xhs": "3.0.0-4060420250429001", "@dcloudio/uni-quickapp-webview": "3.0.0-4060420250429001", "@uni-helper/axios-adapter": "^1.5.2", "@uni-helper/unocss-preset-uni": "^0.2.8", "axios": "^1.6.8", "nutui-uniapp": "^1.8.0", "pinia": "2.0.32", "pinia-plugin-persistedstate": "^3.2.1", "uni-preset-vue": "link:", "uniapp-router-next": "^1.2.7", "vue": "3.5.13", "vue-i18n": "^9.14.4", "wot-design-uni": "^1.9.1", "z-paging": "^2.8.7"}, "devDependencies": {"@babel/core": "^7.24.7", "@babel/eslint-parser": "^7.24.7", "@commitlint/cli": "^19.3.0", "@commitlint/config-conventional": "^19.2.2", "@dcloudio/types": "^3.4.15", "@dcloudio/uni-automator": "3.0.0-4060420250429001", "@dcloudio/uni-cli-shared": "3.0.0-4060420250429001", "@dcloudio/uni-stacktracey": "3.0.0-4060420250429001", "@dcloudio/vite-plugin-uni": "3.0.0-4060420250429001", "@iconify-json/carbon": "^1.2.8", "@iconify-json/ep": "^1.1.15", "@uni-helper/vite-plugin-uni-components": "^0.1.0", "@uni-helper/vite-plugin-uni-layouts": "^0.1.10", "@uni-helper/vite-plugin-uni-manifest": "^0.2.6", "@uni-helper/vite-plugin-uni-pages": "^0.2.28", "@unocss/preset-legacy-compat": "^0.59.4", "@unocss/preset-uno": "^0.59.4", "@vue/runtime-core": "^3.5.13", "commitizen": "^4.3.0", "commitlint": "^19.3.0", "cz-customizable": "^7.0.0", "eslint": "^8.57.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-vue": "^9.23.0", "husky": "^6.0.0", "lint-staged": "^15.2.2", "mockjs": "^1.1.0", "postcss-html": "^1.8.0", "sass": "^1.77.8", "sass-loader": "^14.2.1", "standard": "^17.1.0", "stylelint": "^16.3.1", "stylelint-config-prettier": "^9.0.5", "stylelint-config-recess-order": "^6.0.0", "stylelint-config-recommended-vue": "^1.6.0", "stylelint-config-standard-scss": "^13.1.0", "stylelint-scss": "^6.2.1", "unocss": "^0.58.9", "unocss-applet": "^0.7.8", "unplugin-auto-import": "^19.2.0", "unplugin-uni-router": "^1.2.7", "vite": "5.2.8", "vue-global-api": "^0.4.1"}, "lint-staged": {"src/**/*.{js,jsx,ts,tsx,css,scss}": ["prettier --write", "eslint --fix", "stylelint --fix"]}, "config": {"commitizen": {"path": "node_modules/cz-customizable"}}, "packageManager": "pnpm@10.1.0+sha1.ab7948c89104fdd3fc88b5b391fa4b73fd800631"}